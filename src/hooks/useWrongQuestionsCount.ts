import { useCallback, useEffect, useState } from 'react';
import { WrongQuestionsService } from '../services/questions/wrongQuestionsService';
import { useDatabase } from './useDatabase';

interface UseWrongQuestionsCountReturn {
  count: number;
  loading: boolean;
  error: string | null;
  refreshCount: () => Promise<void>;
}

export function useWrongQuestionsCount(): UseWrongQuestionsCountReturn {
  const { isInitialized, error: dbError } = useDatabase();
  const [count, setCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadCount = useCallback(async () => {
    if (!isInitialized) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Use active wrong questions (excluding mastered ones) for the badge count
      const activeWrongQuestions = await WrongQuestionsService.getActiveWrongQuestions();
      setCount(activeWrongQuestions.length);
    } catch (err) {
      console.error('Failed to load wrong questions count:', err);
      setError(err instanceof Error ? err.message : '無法載入錯題數量');
    } finally {
      setLoading(false);
    }
  }, [isInitialized]);

  const refreshCount = useCallback(async () => {
    await loadCount();
  }, [loadCount]);

  useEffect(() => {
    if (isInitialized && !dbError) {
      loadCount();
    } else if (dbError) {
      setError(dbError);
      setLoading(false);
    }
  }, [isInitialized, dbError, loadCount]);

  return {
    count,
    loading,
    error,
    refreshCount,
  };
}
