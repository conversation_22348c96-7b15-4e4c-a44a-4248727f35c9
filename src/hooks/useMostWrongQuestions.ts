import { useEffect, useState } from 'react';
import { QuestionStatsService } from '../services/database/questionStatsService';
import { QuestionStat } from '../services/database/questionStatsService';

export function useMostWrongQuestions(volume: number) {
  const [mostWrongQuestions, setMostWrongQuestions] = useState<QuestionStat[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadMostWrongQuestions();
  }, [volume]);

  const loadMostWrongQuestions = async () => {
    try {
      setLoading(true);
      setError(null);
      const questionStatsService = QuestionStatsService.getInstance();
      const wrongQuestions = await questionStatsService.getMostWrongQuestions(volume, 5);
      setMostWrongQuestions(wrongQuestions);
    } catch (err) {
      console.error('Failed to load most wrong questions:', err);
      setError(err instanceof Error ? err.message : 'Failed to load wrong questions');
    } finally {
      setLoading(false);
    }
  };

  return {
    mostWrongQuestions,
    loading,
    error,
    refetch: loadMostWrongQuestions,
  };
}
