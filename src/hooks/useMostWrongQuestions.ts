import { useEffect, useState } from 'react';
import { QuestionStat, QuestionStatsService } from '../services/database/questionStatsService';
import { QuestionLoader } from '../services/questions/loader';
import { ProcessedQuestion } from '../types/question';

export interface WrongQuestionWithData {
  stats: QuestionStat;
  question: ProcessedQuestion;
}

export function useMostWrongQuestions(volume: number) {
  const [mostWrongQuestions, setMostWrongQuestions] = useState<WrongQuestionWithData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadMostWrongQuestions();
  }, [volume]);

  const loadMostWrongQuestions = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get wrong question stats
      const questionStatsService = QuestionStatsService.getInstance();
      const wrongQuestionStats = await questionStatsService.getMostWrongQuestions(volume, 5);

      if (wrongQuestionStats.length === 0) {
        setMostWrongQuestions([]);
        return;
      }

      // Load all questions for this volume
      const allQuestions = await QuestionLoader.loadVolume(volume);

      // Match stats with actual question data
      const wrongQuestionsWithData: WrongQuestionWithData[] = [];

      for (const stat of wrongQuestionStats) {
        const question = allQuestions.find(q => q.id === stat.questionId);
        if (question) {
          wrongQuestionsWithData.push({
            stats: stat,
            question: question
          });
        }
      }

      setMostWrongQuestions(wrongQuestionsWithData);
    } catch (err) {
      console.error('Failed to load most wrong questions:', err);
      setError(err instanceof Error ? err.message : 'Failed to load wrong questions');
    } finally {
      setLoading(false);
    }
  };

  return {
    mostWrongQuestions,
    loading,
    error,
    refetch: loadMostWrongQuestions,
  };
}
