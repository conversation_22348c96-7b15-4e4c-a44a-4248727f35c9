import { useCallback, useEffect, useState } from 'react';
import { statisticsCalculator } from '../services/statistics/calculator';
import { usePreferencesStore } from '../store/usePreferencesStore';
import { OverallStats, PredictedPassRate } from '../types/statistics';
import { VOLUMES } from '../utils/constants';
import { updateWidgetData } from '../utils/widgetStorage';
import { useDatabase } from './useDatabase';

interface UseStatisticsReturn {
  overallStats: OverallStats | null;
  predictedPassRate: PredictedPassRate | null;
  volumeDisplayStats: {
    volume: number;
    title: string;
    total: number;
    correct: number;
    wrong: number;
    unseen: number;
  }[] | null;
  loading: boolean;
  error: string | null;
  refreshStats: () => Promise<void>;
}

export function useStatistics(): UseStatisticsReturn {
  const { isInitialized, error: dbError } = useDatabase();
  const { examDate } = usePreferencesStore();
  const [overallStats, setOverallStats] = useState<OverallStats | null>(null);
  const [predictedPassRate, setPredictedPassRate] = useState<PredictedPassRate | null>(null);
  const [volumeDisplayStats, setVolumeDisplayStats] = useState<any[] | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadStatistics = useCallback(async () => {
    // Don't load if database is not initialized
    if (!isInitialized) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Load all statistics concurrently
      const [overall, prediction, volumeDisplay] = await Promise.all([
        statisticsCalculator.calculateOverallStats(),
        statisticsCalculator.calculatePredictedPassRate(),
        statisticsCalculator.getVolumeDisplayStats(),
      ]);

      setOverallStats(overall);
      setPredictedPassRate(prediction);
      setVolumeDisplayStats(volumeDisplay);

      // Update widget with latest statistics
      if (overall) {


        // Calculate total available questions across all volumes
        const totalAvailableQuestions = Object.values(VOLUMES.COUNTS).reduce((sum, count) => sum + count, 0);

        updateWidgetData({
          totalAvailableQuestions,
          correctAnswers: overall.correctAnswers,
          totalAnswered: overall.totalQuestionsAnswered,
          examDate: examDate || '未設定',
        });
      }
    } catch (err) {
      console.error('Failed to load statistics:', err);
      setError(err instanceof Error ? err.message : '無法載入統計數據');
    } finally {
      setLoading(false);
    }
  }, [isInitialized, examDate]);

  const refreshStats = useCallback(async () => {
    await loadStatistics();
  }, [loadStatistics]);

  useEffect(() => {
    if (isInitialized && !dbError) {
      loadStatistics();
    } else if (dbError) {
      setError(dbError);
      setLoading(false);
    }
  }, [isInitialized, dbError, loadStatistics]);

  return {
    overallStats,
    predictedPassRate,
    volumeDisplayStats,
    loading,
    error,
    refreshStats,
  };
}