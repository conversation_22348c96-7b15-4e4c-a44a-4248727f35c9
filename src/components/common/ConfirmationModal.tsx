import React from 'react';
import { Modal, Text, TouchableOpacity, View } from 'react-native';

interface ConfirmationModalProps {
  visible: boolean;
  title: string;
  content: string[];
  onCancel: () => void;
  onConfirm: () => void;
  cancelText?: string;
  confirmText?: string;
}

export const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  visible,
  title,
  content,
  onCancel,
  onConfirm,
  cancelText = '取消',
  confirmText = '確認',
}) => {
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onCancel}
    >
      <View className="flex-1 bg-black/50 justify-center items-center p-5">
        <View
          className="bg-card rounded-2xl w-full shadow-lg"
          style={{
            maxWidth: 340,
            shadowColor: '#000000',
            shadowOffset: { width: 0, height: 8 },
            shadowOpacity: 0.25,
            shadowRadius: 16,
            elevation: 8
          }}
        >
          <View className="pt-6 px-6 pb-4 border-b border-border">
            <Text className="text-20 font-semibold text-text text-center">{title}</Text>
          </View>

          <View className="p-6">
            {content.map((text, index) => (
              <Text
                key={index}
                className={`text-center ${index === 0 ? 'text-16 text-text font-medium' : 'text-14 text-text-light leading-5 mt-3'}`}
              >
                {text}
              </Text>
            ))}
          </View>

          <View className="flex-row border-t border-border">
            <TouchableOpacity
              className="flex-1 py-4 px-6 items-center border-r border-border"
              onPress={onCancel}
            >
              <Text className="text-16 font-semibold text-text-light">{cancelText}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              className="flex-1 py-4 px-6 items-center bg-theme rounded-br-2xl"
              onPress={onConfirm}
            >
              <Text className="text-16 font-semibold text-white">
                {confirmText}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};


