import React from 'react';
import {
  ActivityIndicator,
  Text,
  View,
} from 'react-native';

interface LoadingSpinnerProps {
  size?: 'small' | 'large';
  color?: string;
  message?: string;
  overlay?: boolean;
}

export function LoadingSpinner({
  size = 'large',
  color = '#007AFF',
  message,
  overlay = false,
}: LoadingSpinnerProps) {
  return (
    <View className={`justify-center items-center ${overlay ? 'absolute top-0 left-0 right-0 bottom-0 bg-black/20' : ''}`}>
      <View className="items-center">
        <ActivityIndicator size={size} color={color} />
        {message && (
          <Text className="text-base text-text mt-2 text-center">{message}</Text>
        )}
      </View>
    </View>
  );
}

