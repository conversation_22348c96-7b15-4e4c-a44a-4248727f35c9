import React from 'react';
import {
    Modal as RNModal,
    Text,
    TouchableWithoutFeedback,
    View,
} from 'react-native';
import { Button } from './Button';

interface ModalProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  primaryAction?: {
    title: string;
    onPress: () => void;
    variant?: 'primary' | 'secondary' | 'outline';
  };
  secondaryAction?: {
    title: string;
    onPress: () => void;
  };
  dismissible?: boolean;
}

export function Modal({
  visible,
  onClose,
  title,
  children,
  primaryAction,
  secondaryAction,
  dismissible = true,
}: ModalProps) {
  const handleBackdropPress = () => {
    if (dismissible) {
      onClose();
    }
  };

  return (
    <RNModal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={handleBackdropPress}>
        <View className="flex-1 bg-black/50 justify-center items-center p-5">
          <TouchableWithoutFeedback>
            <View className="bg-white rounded-2xl max-w-[400px] w-full max-h-[80%] shadow-lg">
              {title && (
                <View className="pt-6 px-6 pb-4 border-b border-gray-200">
                  <Text className="text-xl font-semibold text-gray-900 text-center">{title}</Text>
                </View>
              )}

              <View className="p-6 flex-1">
                {children}
              </View>

              {(primaryAction || secondaryAction) && (
                <View className="flex-row px-6 pb-6 gap-3">
                  {secondaryAction && (
                    <Button
                      title={secondaryAction.title}
                      onPress={secondaryAction.onPress}
                      variant="outline"
                      style={{ flex: 1 }}
                    />
                  )}
                  {primaryAction && (
                    <Button
                      title={primaryAction.title}
                      onPress={primaryAction.onPress}
                      variant={primaryAction.variant || 'primary'}
                      style={{ flex: 1 }}
                    />
                  )}
                </View>
              )}
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </RNModal>
  );
}