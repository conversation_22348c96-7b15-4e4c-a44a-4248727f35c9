import React from 'react';
import {
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';

interface CardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  onPress?: () => void;
  variant?: 'default' | 'elevated' | 'outlined';
  className?: string;
}

// Helper function to get card classes based on variant
const getCardClasses = (variant: string, className?: string) => {
  const baseClasses = "rounded-xl p-4";

  const variantClasses = {
    default: "bg-white border border-border",
    elevated: "bg-white shadow-lg",
    outlined: "bg-transparent border-2 border-theme"
  } as any;

  return `${baseClasses} ${variantClasses[variant]} ${className || ''}`.trim();
};

export function Card({
  children,
  style,
  onPress,
  variant = 'default',
  className
}: CardProps) {
  const cardClasses = getCardClasses(variant, className);

  if (onPress) {
    return (
      <TouchableOpacity
        className={cardClasses}
        style={style}
        onPress={onPress}
        activeOpacity={0.8}
      >
        {children}
      </TouchableOpacity>
    );
  }

  return (
    <View className={cardClasses} style={style}>
      {children}
    </View>
  );
}

