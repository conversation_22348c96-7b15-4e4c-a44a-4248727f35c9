import React from 'react';
import {
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

interface HeaderProps {
  title: string;
  leftAction?: {
    icon: string;
    onPress: () => void;
  };
  rightAction?: {
    icon: string;
    onPress: () => void;
  };
  showProfile?: boolean;
}

export function Header({
  title,
  leftAction,
  rightAction,
  showProfile = false,
}: HeaderProps) {
  return (
    <SafeAreaView edges={['top']} className="bg-white border-b border-border">
      <View className="flex-row items-center justify-between px-4 py-3">
        <View className="flex-1">
          {leftAction && (
            <TouchableOpacity
              className="w-10 h-10 items-center justify-center rounded-full"
              onPress={leftAction.onPress}
            >
              <Text className="text-18 text-theme">{leftAction.icon}</Text>
            </TouchableOpacity>
          )}
        </View>

        <View className="flex-2 items-center">
          <Text className="text-18 font-semibold text-text">{title}</Text>
        </View>

        <View className="flex-1 items-end">
          {showProfile && (
            <TouchableOpacity
              className="w-10 h-10 items-center justify-center rounded-full bg-gray-100"
              onPress={() => {}}
            >
              <Text className="text-16">👤</Text>
            </TouchableOpacity>
          )}
          {rightAction && (
            <TouchableOpacity
              className="w-10 h-10 items-center justify-center rounded-full"
              onPress={rightAction.onPress}
            >
              <Text className="text-18 text-theme">{rightAction.icon}</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </SafeAreaView>
  );
}

