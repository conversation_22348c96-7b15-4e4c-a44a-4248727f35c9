import React, { useRef } from 'react';
import {
  ActivityIndicator,
  Animated,
  Platform,
  Text,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';

import { COLORS } from '../../utils/constants';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'small' | 'medium' | 'large' | 'custom';
  disabled?: boolean;
  loading?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  badge?: {
    count: number;
    color?: string;
  };
}

// Helper function to get button classes based on variant and size
const getButtonClasses = (variant: string, size: string, disabled: boolean) => {
  const baseClasses = "rounded-[12px] items-center justify-center border";

  // Variant classes
  const variantClasses = {
    primary: "bg-theme border-theme",
    secondary: "bg-card border-border",
    outline: "bg-theme border-theme"
  } as any;

  // Size classes
  const sizeClasses = {
    small: "py-2 px-4 min-h-[36px]",
    medium: Platform.OS === 'ios' ? "py-3 px-5 min-h-44" : "py-2 px-5 min-h-44",
    large: "py-3 px-6 min-h-52"
  } as any;

  const disabledClass = disabled ? "opacity-60" : "";

  return `${baseClasses} ${variantClasses[variant]} ${size !== 'custom' ? sizeClasses[size] : ''} ${disabledClass}`.trim();
};

// Helper function to get text classes based on variant and size
const getTextClasses = (variant: 'primary' | 'secondary' | 'outline', size: 'small' | 'medium' | 'large' | 'custom', disabled: boolean) => {
  const baseClasses = "font-semibold text-center";

  // Variant text colors
  const variantTextClasses = {
    primary: "text-white",
    secondary: "text-text",
    outline: "text-white"
  };

  // Size text classes
  const sizeTextClasses = {
    small: "text-sm",
    medium: "text-base",
    large: "text-xl",
    custom: "text-base"
  };

  const disabledClass = disabled ? "opacity-60" : "";

  return `${baseClasses} ${variantTextClasses[variant]} ${sizeTextClasses[size]} ${disabledClass}`.trim();
};

export function Button({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  style,
  textStyle,
  badge,
}: ButtonProps) {
  const scaleAnimate = useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    if (disabled || loading) {
      return;
    }

    Animated.spring(scaleAnimate, {
      toValue: 0.95,
      useNativeDriver: true,
      speed: 20,
      bounciness: 4,
    }).start();
  };

  const handlePressOut = () => {
    if (disabled || loading) {
      return;
    }

    Animated.spring(scaleAnimate, {
      toValue: 1,
      useNativeDriver: true,
      speed: 20,
      bounciness: 4,
    }).start();
  };

  const animatedStyle = [
    { flex: 1, alignSelf: 'stretch' }, // Apply full width to animated wrapper
    {
      transform: [{ scale: scaleAnimate }],
    },
  ];

  const buttonClasses = getButtonClasses(variant, size, disabled);
  const textClasses = getTextClasses(variant, size, disabled);

  const showBadge = badge && badge.count > 0;

  return (
    <Animated.View style={animatedStyle as any}>
      <TouchableOpacity
        className={buttonClasses}
        style={style}
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled || loading}
        activeOpacity={0.9}
      >
        <View className="flex-row items-center justify-center relative">
          {loading ? (
            <ActivityIndicator
              size="small"
              color={variant === 'primary' ? '#ffffff' : COLORS.THEME}
            />
          ) : (
            <Text className={textClasses} style={textStyle}>{title}</Text>
          )}
        </View>
        {showBadge && (
          <View
            className="absolute -top-2 -right-2 rounded-[10px] min-w-[20px] h-5 justify-center items-center px-1"
            style={{ backgroundColor: badge?.color || COLORS.ERROR }}
          >
            <Text className="text-white text-[11px] font-bold text-center">
              {badge!.count.toString()}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    </Animated.View>
  );
}