import { BottomSheetModal, BottomSheetScrollView, BottomSheetView } from '@gorhom/bottom-sheet';
import { router } from 'expo-router';
import React, { useMemo, useState } from 'react';
import { Alert, Text, View } from 'react-native';

import { useStatistics } from '../hooks/useStatistics';
import { QuestionManager } from '../services/questions/manager';
import { usePracticeStore } from '../store/usePracticeStore';
import { PracticeConfig } from '../types/session';
import { COLORS, VOLUMES } from '../utils/constants';
import { <PERSON><PERSON>, Card } from './common';

interface PracticeOptionsBottomSheetProps {
  bottomSheetModalRef: React.RefObject<BottomSheetModal>;
}

export function PracticeOptionsBottomSheet({ bottomSheetModalRef }: PracticeOptionsBottomSheetProps) {
  const [loading, setLoading] = useState(false);
  const [selectedVolumes, setSelectedVolumes] = useState<number[]>([]);
  const [includeUnse<PERSON>, setIncludeUnseen] = useState(true);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [practiceMode, setPracticeMode] = useState<'sequential' | 'random'>('sequential');
  const { startSession } = usePracticeStore();
  const { volumeDisplayStats } = useStatistics();

  // Variables
  const snapPoints = useMemo(() => ['70%', '95%'], []);

  const startPractice = async (config: PracticeConfig) => {
    setLoading(true);

    try {
      const questions = await QuestionManager.generatePracticeQuestions(config);

      if (questions.length === 0) {
        Alert.alert('沒有題目', '所選範圍內沒有找到題目，請選擇其他選項。');
        return;
      }

      startSession(config, questions);
      bottomSheetModalRef.current?.dismiss();
      router.push('/practice/session');
    } catch (error) {
      console.error('Failed to start practice:', error);
      Alert.alert('錯誤', '無法載入練習題目，請稍後再試。');
    } finally {
      setLoading(false);
    }
  };

  const toggleVolumeSelection = (volume: number) => {
    setSelectedVolumes(prev => {
      if (prev.includes(volume)) {
        return prev.filter(v => v !== volume);
      } else {
        return [...prev, volume].sort();
      }
    });
  };

  const handleStartPractice = () => {
    if (selectedVolumes.length === 0) {
      Alert.alert('請選擇冊別', '請至少選擇一個冊別進行練習。');
      return;
    }

    const config: PracticeConfig = {
      volumes: selectedVolumes,
      chapter: null,
      mode: practiceMode,
      includeWrongQuestions: false,
      includeBookmarked: false,
      includeUnseen,
    };

    startPractice(config);
  };

  return (
    <BottomSheetModal
      ref={bottomSheetModalRef}
      index={1}
      snapPoints={snapPoints}
      onChange={() => {}}
      enablePanDownToClose
      backgroundStyle={{ backgroundColor: COLORS.BACKGROUND }}
      handleIndicatorStyle={{ backgroundColor: COLORS.BORDER }}
    >
      <BottomSheetView className="flex flex-col">
        {/* Header */}
        <View className="flex-row justify-between items-center px-5 py-4 border-b border-border">
          <Text className="text-20 font-bold text-text tracking-tight">選擇練習模式</Text>
          <Button
            title="✕"
            onPress={() => bottomSheetModalRef.current?.dismiss()}
            variant="secondary"
            style={{ width: 32, height: 32, borderRadius: 16, backgroundColor: 'transparent', borderWidth: 0 }}
            textStyle={{ fontSize: 14, color: '#2C2C2E', fontWeight: '600' }}
          />
        </View>

        {/* Scrollable Content */}
        <BottomSheetScrollView
          contentContainerClassName="pb-5"
          showsVerticalScrollIndicator={false}
          className="flex-1"
        >
          {/* Volume Selection */}
          <View className="mb-6 px-5">
            <View className="mb-4">
              <Text className="text-22 font-bold mb-1.5 text-text tracking-tight">選擇練習冊別</Text>
              <Text className="text-15 text-text-light leading-5">可選擇一個或多個冊別進行練習</Text>
            </View>

            <View className="flex-row flex-wrap justify-between">
              {Array.from({ length: VOLUMES.TOTAL }, (_, i) => i + 1).map(volume => {
                const isSelected = selectedVolumes.includes(volume);
                const volumeStats = volumeDisplayStats?.find(v => v.volume === volume);
                const accuracy = volumeStats ?
                  (volumeStats.correct / Math.max(1, volumeStats.correct + volumeStats.wrong) * 100) : 0;

                return (
                  <Card
                    key={volume}
                    className={`w-1/2 mb-4 border-2 rounded-2xl bg-card shadow-sm p-4 ${
                      isSelected
                        ? 'border-accent border-3 shadow-sm shadow-shadow/12'
                        : 'border-transparent shadow-shadow/8'
                    }`}
                    onPress={() => toggleVolumeSelection(volume)}
                  >
                    <View className="flex-row justify-between items-center mb-1">
                      <Text className={`text-16 font-semibold text-text ${isSelected ? 'font-bold' : ''}`}>
                        第 {volume} 冊
                      </Text>
                      <View className={`w-5.5 h-5.5 rounded-full border-2 items-center justify-center ${
                        isSelected ? 'bg-accent border-accent' : 'border-border bg-card'
                      }`}>
                        {isSelected && <Text className="text-white text-12 font-bold">✓</Text>}
                      </View>
                    </View>
                    <Text className={`text-14 text-text mb-2 ${isSelected ? '' : ''}`}>
                      {volumeStats?.title || VOLUMES.NAMES[volume as keyof typeof VOLUMES.NAMES]}
                    </Text>

                    {volumeStats && (volumeStats.correct + volumeStats.wrong > 0) ? (
                      <View className="mt-1">
                        <Text className={`text-12 font-semibold mb-0.5 ${
                          accuracy >= 80 ? 'text-success' : accuracy >= 60 ? 'text-warning' : 'text-error'
                        }`}>
                          正確率: {accuracy.toFixed(0)}%
                        </Text>
                        <Text className="text-12 text-text-secondary">
                          已練習 {volumeStats.correct + volumeStats.wrong} / {volumeStats.total} 題
                        </Text>
                      </View>
                    ) : (
                      <Text className="text-12 text-text-secondary">
                        共 {volumeStats?.total || VOLUMES.COUNTS[volume as keyof typeof VOLUMES.COUNTS]} 道題目 · 未開始練習
                      </Text>
                    )}
                  </Card>
                );
              })}
            </View>
          </View>

          {/* Practice Options */}
          <View className="mb-6 px-5">
            <Text className="text-22 font-bold mb-1.5 text-text tracking-tight">練習選項</Text>

            <Card className="mb-3">
              <View className="flex-row justify-between items-center mb-2">
                <Text className="text-16 font-medium text-text">題目範圍</Text>
                <View className="flex-row gap-2">
                  <Button
                    title="只練習未做過的"
                    onPress={() => setIncludeUnseen(true)}
                    variant={includeUnseen ? 'primary' : 'secondary'}
                    size="small"
                    style={{ paddingHorizontal: 12 }}
                  />
                  <Button
                    title="全部題目"
                    onPress={() => setIncludeUnseen(false)}
                    variant={!includeUnseen ? 'primary' : 'secondary'}
                    size="small"
                    style={{ paddingHorizontal: 12 }}
                  />
                </View>
              </View>
            </Card>

            {/* Advanced Settings */}
            <Card className="mb-3">
              <View className="flex-row justify-between items-center mb-2">
                <Text className="text-16 font-medium text-text">進階設定</Text>
                <Button
                  title={showAdvanced ? "收起 ▲" : "展開 ▼"}
                  onPress={() => setShowAdvanced(!showAdvanced)}
                  variant="secondary"
                  size="small"
                />
              </View>

              {showAdvanced && (
                <View className="pt-3 border-t border-border">
                  <View className="flex-row justify-between items-center mb-2">
                    <Text className="text-14 font-medium text-text">出題順序</Text>
                    <View className="flex-row gap-2">
                      <Button
                        title="按順序"
                        onPress={() => setPracticeMode('sequential')}
                        variant={practiceMode === 'sequential' ? 'primary' : 'secondary'}
                        size="small"
                        style={{ paddingHorizontal: 12 }}
                      />
                      <Button
                        title="隨機"
                        onPress={() => setPracticeMode('random')}
                        variant={practiceMode === 'random' ? 'primary' : 'secondary'}
                        size="small"
                        style={{ paddingHorizontal: 12 }}
                      />
                    </View>
                  </View>
                </View>
              )}
            </Card>
          </View>

          {/* Bottom padding to account for sticky button */}
          <View className="h-[100px]" />
        </BottomSheetScrollView>

        {/* Sticky Bottom Button */}
        <View className="absolute bottom-0 left-0 right-0 p-5 bg-white border-t border-border">
          <Button
            title={selectedVolumes.length > 0 ? `開始練習 (已選 ${selectedVolumes.length} 冊)` : '開始練習'}
            onPress={handleStartPractice}
            disabled={selectedVolumes.length === 0 || loading}
            style={{
              backgroundColor: selectedVolumes.length > 0 ? COLORS.THEME : COLORS.CARD_BACKGROUND,
              borderColor: selectedVolumes.length > 0 ? COLORS.THEME : COLORS.BORDER
            }}
          />
        </View>
      </BottomSheetView>
    </BottomSheetModal>
  );
}
