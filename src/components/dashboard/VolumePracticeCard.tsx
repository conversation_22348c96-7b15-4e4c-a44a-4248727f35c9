import { useRouter } from 'expo-router';
import React, { useRef } from 'react';
import { Alert, Animated, ImageBackground, Pressable, Text, View } from 'react-native';
import { COLORS } from '../../utils/constants';

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

// Volume illustration images mapping
const VOLUME_ILLUSTRATIONS = {
  1: require('../../../assets/images/illustrations/volume_1.png'),
  2: require('../../../assets/images/illustrations/volume_2.png'),
  3: require('../../../assets/images/illustrations/volume_3.png'),
  4: require('../../../assets/images/illustrations/volume_4.png'),
  5: require('../../../assets/images/illustrations/volume_5.png'),
} as const;

// Chinese number mapping
const CHINESE_NUMBERS = {
  1: '一',
  2: '二',
  3: '三',
  4: '四',
  5: '五',
} as const;

type VolumeStatsDisplay = {
  volume: number;
  title: string;
  correct: number;
  wrong: number;
  unseen: number;
  total: number;
};

type VolumePracticeCardProps = {
  volumeStats: VolumeStatsDisplay;
};

const VolumePracticeCard: React.FC<VolumePracticeCardProps> = ({ volumeStats }) => {
  const router = useRouter();
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const isDisabled = volumeStats.volume < 1;

  const handlePressIn = () => {
    if (isDisabled) {
      return;
    }

    Animated.spring(scaleAnim, {
      toValue: 0.95,
      useNativeDriver: true,
      speed: 20,
      bounciness: 4,
    }).start();
  };

  const handlePressOut = () => {
    if (isDisabled) {
      return;
    }

    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
      speed: 20,
      bounciness: 4,
    }).start();
  };

  const handlePress = () => {
    if (isDisabled) {
      Alert.alert('即將推出', '此冊別練習功能即將推出，敬請期待！');
      return;
    }

    // Navigate to volume entry page
    router.push(`/practice/volume/${volumeStats.volume}` as any);
  };

  const progressData = [
    { value: volumeStats.correct, color: COLORS.SUCCESS }, // Green for correct
    { value: volumeStats.wrong, color: COLORS.ERROR },     // Red for wrong
    { value: volumeStats.unseen, color: COLORS.BORDER },   // Gray for unseen
  ];

  const backgroundImage = VOLUME_ILLUSTRATIONS[volumeStats.volume as keyof typeof VOLUME_ILLUSTRATIONS] || null;

  // Calculate question counts for display
  const remainingQuestions = volumeStats.unseen + volumeStats.wrong;
  const isCompleted = remainingQuestions === 0;
  const displayCount = isCompleted ? volumeStats.correct : remainingQuestions;
  const countLabel = isCompleted ? '已完成' : '剩';

  // Extract the common content area to avoid duplication
  const renderContent = () => (
    <View className="flex-1 justify-end">
      <Text className={`text-11 font-bold uppercase tracking-wider opacity-90 ${isDisabled ? 'opacity-70' : ''}`} style={{ color: isDisabled ? COLORS.TEXT_LIGHT : COLORS.THEME_TEXT }}>
        第{CHINESE_NUMBERS[volumeStats.volume as keyof typeof CHINESE_NUMBERS] || volumeStats.volume}冊
      </Text>
      <View className="flex-row items-baseline justify-between">
        <Text className={`text-14 font-bold mt-1.5 leading-4 text-center ${isDisabled ? 'opacity-70' : ''}`} style={{ color: COLORS.TEXT }}>
          {volumeStats.title}
        </Text>
        {!isDisabled && (
          <Text className={`text-11 font-semibold leading-3.5 text-right ${isCompleted ? 'text-success' : 'text-warning'}`}>
            {countLabel} {displayCount} 題
          </Text>
        )}
      </View>
      {isDisabled ? (
        <Text className="text-12 font-semibold text-warning mt-1">即將推出</Text>
      ) : null}
      {!isDisabled && (
        <View className="h-1 bg-black/10 rounded mt-1.5 overflow-hidden flex-row" style={{ shadowColor: 'rgba(0, 0, 0, 0.1)', shadowOffset: { width: 0, height: 1 }, shadowOpacity: 0.3, shadowRadius: 1 }}>
          {progressData.map((segment, index) => {
            const segmentWidth = (segment.value / volumeStats.total) * 100;
            if (segmentWidth === 0) return null;
            return (
              <View
                key={index}
                style={{
                  width: `${segmentWidth}%`,
                  height: '100%',
                  backgroundColor: segment.color,
                }}
              />
            );
          })}
        </View>
      )}
    </View>
  );

  const animatedStyle = {
    transform: [{ scale: scaleAnim }],
  };

  return (
    <AnimatedPressable
      style={[{ minHeight: 200, borderRadius: 16, borderWidth: 1, borderColor: '#BFBFBF20', overflow: 'hidden', shadowColor: '#000000', shadowOffset: { width: 0, height: 2 }, shadowOpacity: 0.05, shadowRadius: 10, elevation: 3 }, animatedStyle]}
      onPress={handlePress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      disabled={false}
    >
      <View
        className={`flex-1 rounded-2xl overflow-hidden ${isDisabled ? 'opacity-60 bg-border border-border' : ''}`}
      >
        {backgroundImage ? (
          <>
            <View className="overflow-hidden rounded-t-2xl" style={{ height: 130 }}>
              <ImageBackground
                source={backgroundImage}
                className="flex-1"
                imageStyle={{ borderTopLeftRadius: 16, borderTopRightRadius: 16 }}
                resizeMode="cover"
              />
            </View>
            <View className="bg-white rounded-b-2xl justify-center" style={{ height: 70, paddingHorizontal: 16, paddingVertical: 12 }}>
              {renderContent()}
            </View>
          </>
        ) : (
          <View className={`flex-1 justify-end rounded-2xl ${isDisabled ? 'bg-card-disabled' : 'bg-gray-200/20'}`} style={{ padding: 18 }}>
            {renderContent()}
          </View>
        )}
      </View>
    </AnimatedPressable>
  );
};



export default VolumePracticeCard;
