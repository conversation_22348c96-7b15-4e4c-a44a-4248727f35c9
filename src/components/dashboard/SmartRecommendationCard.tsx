import React from 'react';
import { Text, View } from 'react-native';
import { OverallStats } from '../../types/statistics';
import { Button, Card } from '../common';

interface SmartRecommendationCardProps {
  stats: OverallStats;
  onStartWeakPointPractice: () => void;
}

export function SmartRecommendationCard({ stats, onStartWeakPointPractice }: SmartRecommendationCardProps) {
  // 計算弱點題目數量（這裡使用模擬數據，實際應該從錯題分析中獲取）
  const weakPointCount = 10;
  
  // 找出正確率最低的冊別作為推薦重點
  const weakestVolume = stats.volumeStats.reduce((weakest, current) => 
    current.accuracy < weakest.accuracy ? current : weakest
  );

  return (
    <Card className="mb-5 bg-white rounded-2xl border-l-4 border-l-accent">
      <View className="flex-row items-center mb-4">
        <View className="w-12 h-12 bg-accent/10 rounded-full items-center justify-center mr-3">
          <Text className="text-20">🎯</Text>
        </View>
        <View className="flex-1">
          <Text className="text-18 font-bold text-text mb-1">每日弱點強攻</Text>
          <Text className="text-12 text-text-secondary">基於個人化錯題分析</Text>
        </View>
      </View>

      <View>
        <Text className="text-16 font-semibold text-text mb-3">
          今日挑戰：你的 {weakPointCount} 道高頻錯題
        </Text>

        <View className="bg-accent/5 rounded-xl p-3 mb-4">
          <Text className="text-14 font-medium text-text mb-1">
            重點加強：第{weakestVolume.volume}冊 (正確率 {weakestVolume.accuracy.toFixed(1)}%)
          </Text>
          <Text className="text-12 text-success font-medium">
            完成後預計提升合格率 3-5%
          </Text>
        </View>

        <View className="flex-row justify-between mb-4">
          <View className="items-center">
            <Text className="text-20 font-bold text-accent">{weakPointCount}</Text>
            <Text className="text-10 text-text-secondary text-center">錯題待攻克</Text>
          </View>
          <View className="items-center">
            <Text className="text-20 font-bold text-accent">5-8</Text>
            <Text className="text-10 text-text-secondary text-center">預計用時(分鐘)</Text>
          </View>
          <View className="items-center">
            <Text className="text-20 font-bold text-accent">+3%</Text>
            <Text className="text-10 text-text-secondary text-center">預期提升</Text>
          </View>
        </View>

        <Button
          title="開始今日挑戰"
          onPress={onStartWeakPointPractice}
        />
      </View>
    </Card>
  );
}


