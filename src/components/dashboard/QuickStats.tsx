import React from 'react';
import { Text, View } from 'react-native';
import { OverallStats } from '../../types/statistics';
import { COLORS } from '../../utils/constants';
import { Card } from '../common';

interface QuickStatsProps {
  stats: OverallStats;
}

export function QuickStats({ stats }: QuickStatsProps) {
  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}小時${minutes}分鐘`;
    }
    return `${minutes}分鐘`;
  };

  const statsData = [
    {
      label: '總答題數',
      value: stats.totalQuestionsAnswered.toString(),
      color: COLORS.PRIMARY,
    },
    {
      label: '整體正確率',
      value: `${stats.accuracy.toFixed(1)}%`,
      color: stats.accuracy >= 80 ? COLORS.SUCCESS : stats.accuracy >= 60 ? COLORS.WARNING : COLORS.ERROR,
    },
    {
      label: '連續答對',
      value: stats.streakCount.toString(),
      color: COLORS.SUCCESS,
    },
    {
      label: '學習時間',
      value: formatTime(stats.totalTimeSpent),
      color: COLORS.PRIMARY,
    },
  ];

  return (
    <Card className="mb-4">
      <Text className="text-18 font-bold text-text mb-4">學習統計</Text>

      <View className="flex-row flex-wrap justify-between">
        {statsData.map((stat, index) => (
          <View key={index} className="w-[48%] items-center mb-4">
            <Text className="text-26 font-bold" style={{ color: stat.color }}>
              {stat.value}
            </Text>
            <Text className="text-12 text-text-secondary text-center">{stat.label}</Text>
          </View>
        ))}
      </View>

      {stats.averageTimePerQuestion > 0 && (
        <View className="pt-4 border-t border-border">
          <Text className="text-14 text-text-secondary text-center">
            平均每題用時：{Math.round(stats.averageTimePerQuestion)}秒
          </Text>
        </View>
      )}
    </Card>
  );
}

