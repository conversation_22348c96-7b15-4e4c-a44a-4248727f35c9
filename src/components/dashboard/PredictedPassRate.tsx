import React from 'react';
import { Text, View } from 'react-native';
import { PredictedPassRate as PredictedPassRateType } from '../../types/statistics';
import { COLORS } from '../../utils/constants';
import { Card } from '../common';

interface PredictedPassRateProps {
  prediction: PredictedPassRateType;
}

export function PredictedPassRate({ prediction }: PredictedPassRateProps) {
  const getColorByPercentage = (percentage: number) => {
    if (percentage >= 80) return COLORS.SUCCESS;
    if (percentage >= 60) return COLORS.WARNING;
    return COLORS.ERROR;
  };

  const getConfidenceColor = (confidence: PredictedPassRateType['confidence']) => {
    switch (confidence) {
      case 'high': return COLORS.SUCCESS;
      case 'medium': return COLORS.WARNING;
      case 'low': return COLORS.ERROR;
      default: return COLORS.SECONDARY_TEXT;
    }
  };

  const getConfidenceText = (confidence: PredictedPassRateType['confidence']) => {
    switch (confidence) {
      case 'high': return '高';
      case 'medium': return '中';
      case 'low': return '低';
      default: return '';
    }
  };

  return (
    <Card className="mb-4 border-l-4 border-l-primary">
      <View className="flex-row justify-between items-center mb-4">
        <Text className="text-18 font-bold text-text">考試通過率預測</Text>
        <View className="bg-primary/10 px-3 py-1 rounded-full">
          <Text className="text-12 font-semibold" style={{ color: getConfidenceColor(prediction.confidence) }}>
            可信度：{getConfidenceText(prediction.confidence)}
          </Text>
        </View>
      </View>

      <View className="items-center mb-4">
        <Text className="text-48 font-bold" style={{ color: getColorByPercentage(prediction.percentage) }}>
          {prediction.percentage.toFixed(0)}%
        </Text>
        <Text className="text-14 text-text-secondary">預測通過率</Text>
      </View>

      {prediction.recommendations.length > 0 && (
        <View className="bg-background rounded-xl p-4">
          <Text className="text-16 font-semibold text-text mb-3">建議</Text>
          {prediction.recommendations.map((recommendation, index) => (
            <Text key={index} className="text-14 text-text mb-2">
              • {recommendation}
            </Text>
          ))}
        </View>
      )}
    </Card>
  );
}

