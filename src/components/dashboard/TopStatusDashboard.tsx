import React from 'react';
import { Text, View } from 'react-native';
import Svg, { Circle } from 'react-native-svg';
import { OverallStats, PredictedPassRate } from '../../types/statistics';
import { COLORS } from '../../utils/constants';
import { Card } from '../common';

interface TopStatusDashboardProps {
  stats: OverallStats;
  prediction: PredictedPassRate;
  userName?: string;
}

export function TopStatusDashboard({ stats, prediction }: TopStatusDashboardProps) {

  const getColorByPercentage = (percentage: number) => {
    if (percentage >= 80) return COLORS.SUCCESS;
    if (percentage >= 60) return COLORS.WARNING;
    return COLORS.ERROR;
  };

  // Calculate overall progress across all volumes (1-5)
  const availableVolumeStats = stats.volumeStats.filter(vol => vol.volume >= 1);
  const masteredQuestions = availableVolumeStats.reduce((sum, vol) => sum + vol.correctAnswers, 0);
  const totalQuestions = availableVolumeStats.reduce((sum, vol) => sum + vol.totalQuestions, 0);
  
  // Calculate overall progress percentage for available volumes
  const programProgress = totalQuestions > 0 ? (masteredQuestions / totalQuestions) * 100 : 0;

  // Circle progress calculations
  const size = 100;
  const strokeWidth = 8;
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (programProgress / 100) * circumference;

  return (
    <Card className="mb-5 bg-card rounded-[20px] py-6 px-6">
      <View className="flex-row justify-between items-center">
        {/* 左側統計數據 */}
        <View className="flex-1">
          <View className="mb-4">
            <Text className="text-20 font-bold text-text">{masteredQuestions} / {totalQuestions}</Text>
            <Text className="text-13 text-text-light mt-1 font-medium">已掌握題目</Text>
          </View>
          <View>
            <Text className="text-20 font-bold text-text">{stats.accuracy.toFixed(1)}%</Text>
            <Text className="text-13 text-text-light mt-1 font-medium">總正確率</Text>
          </View>
        </View>

        {/* 右側圓形圖表 */}
        <View className="items-center justify-center">
          <View className="relative items-center justify-center">
            <Svg width={size} height={size}>
              {/* Background circle */}
              <Circle
                cx={size / 2}
                cy={size / 2}
                r={radius}
                stroke={COLORS.BORDER}
                strokeWidth={strokeWidth}
                fill="transparent"
              />
              {/* Progress circle */}
              <Circle
                cx={size / 2}
                cy={size / 2}
                r={radius}
                stroke={getColorByPercentage(programProgress)}
                strokeWidth={strokeWidth}
                fill="transparent"
                strokeDasharray={strokeDasharray}
                strokeDashoffset={strokeDashoffset}
                strokeLinecap="round"
                transform={`rotate(-90 ${size / 2} ${size / 2})`}
              />
            </Svg>
            <View className="absolute items-center justify-center w-full h-full">
              <Text className="text-22 font-bold" style={{ color: getColorByPercentage(programProgress) }}>
                {Math.round(programProgress)}%
              </Text>
              <Text className="text-11 text-text-light font-medium">整體進度</Text>
            </View>
          </View>
        </View>
      </View>
    </Card>
  );
}


