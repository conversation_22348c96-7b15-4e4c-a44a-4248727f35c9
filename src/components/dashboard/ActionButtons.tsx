import React from 'react';
import { View } from 'react-native';

import { Button } from '../common';

type ActionButtonsProps = {
  onStartExam: () => void;
  onStartWrongQuestionsReview: () => void;
  wrongQuestionsCount?: number;
};

export function ActionButtons({
  onStartExam,
  onStartWrongQuestionsReview,
  wrongQuestionsCount = 0,
}: ActionButtonsProps) {
  return (
    <View className="flex-row justify-between gap-3 mb-1">
      <Button
        title="開始考試"
        onPress={onStartExam}
        variant="primary"
        size="large"
        style={{ flex: 1 }}
      />
      <Button
        title="錯題複習"
        onPress={onStartWrongQuestionsReview}
        variant="secondary"
        size="large"
        style={{ flex: 1 }}
        badge={wrongQuestionsCount > 0 ? { count: wrongQuestionsCount } : undefined}
      />
    </View>
  );
}

