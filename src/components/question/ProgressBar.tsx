import React from 'react';
import {
  Text,
  View,
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  withTiming,
} from 'react-native-reanimated';

interface ProgressBarProps {
  current: number;
  total: number;
  showNumbers?: boolean;
  color?: string;
}

export function ProgressBar({
  current,
  total,
  showNumbers = true,
  color = '#007AFF',
}: ProgressBarProps) {
  const progress = Math.min(current / total, 1);

  const progressStyle = useAnimatedStyle(() => {
    return {
      width: withTiming(`${progress * 100}%`, {
        duration: 300,
      }),
    };
  });

  return (
    <View className="w-full">
      {showNumbers && (
        <View className="flex-row justify-between items-center mb-2">
          <Text className="text-14 text-[#8E8E93]">
            進度：{current} / {total}
          </Text>
          <Text className="text-14 font-semibold text-[#1c1c1e]">
            {Math.round(progress * 100)}%
          </Text>
        </View>
      )}

      <View className="w-full">
        <View className="h-2 bg-[#E5E5EA] rounded overflow-hidden">
          <Animated.View
            className="h-full rounded"
            style={[
              { backgroundColor: color },
              progressStyle,
            ]}
          />
        </View>
      </View>
    </View>
  );
}