import { LinearGradient } from 'expo-linear-gradient';
import React, { forwardRef } from 'react';
import { LayoutChangeEvent, NativeScrollEvent, NativeSyntheticEvent, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export type QuestionState = 'unanswered' | 'answered' | 'correct' | 'wrong';

interface QuestionNumbersBarProps {
  totalQuestions: number;
  currentIndex: number;
  questionStates: QuestionState[];
  onQuestionPress: (index: number) => void;
  onLayout?: (event: LayoutChangeEvent) => void;
  onScroll?: (event: NativeSyntheticEvent<NativeScrollEvent>) => void;
}

const styles = StyleSheet.create({
  leftGradient: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    zIndex: 10,
    width: 20,
  },
  rightGradient: {
    position: 'absolute',
    right: 0,
    top: 0,
    bottom: 0,
    zIndex: 10,
    width: 40,
  },
});

export const QuestionNumbersBar = forwardRef<ScrollView, QuestionNumbersBarProps>(
  ({ totalQuestions, currentIndex, questionStates, onQuestionPress, onLayout, onScroll }, ref) => {

    return (
      <View className="bg-card border-b border-border py-2.5 relative">
        {/* Left gradient fade */}
        <LinearGradient
          colors={['rgba(255, 255, 255, 1)', 'rgba(255, 255, 255, 1)', 'rgba(255, 255, 255, 0)']}
          start={{ x: 0, y: 0.5 }}
          end={{ x: 1, y: 0.5 }}
          style={styles.leftGradient}
        />

        {/* Right gradient fade */}
        <LinearGradient
          colors={['rgba(255, 255, 255, 0)', 'rgba(255, 255, 255, 1)', 'rgba(255, 255, 255, 1)']}
          start={{ x: 0, y: 0.5 }}
          end={{ x: 1, y: 0.5 }}
          style={styles.rightGradient}
        />

        <View>
          <ScrollView
            ref={ref}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{ paddingHorizontal: 20, gap: 8 }}
            onLayout={onLayout}
            onScroll={onScroll}
            scrollEventThrottle={16}
          >
          {Array.from({ length: totalQuestions }, (_, index) => {
            const state = questionStates[index] || 'unanswered';
            const isCurrent = index === currentIndex;

            const getButtonClassName = () => {
              const baseClass = "w-10 h-10 rounded items-center justify-center";

              if (isCurrent) {
                return `${baseClass} bg-theme`;
              }

              switch (state) {
                case 'answered':
                  return `${baseClass} bg-success`;
                case 'correct':
                  return `${baseClass} bg-success`;
                case 'wrong':
                  return `${baseClass} bg-error`;
                default:
                  return `${baseClass} bg-border`;
              }
            };

            const getTextClassName = () => {
              if (isCurrent) {
                return "text-14 font-semibold text-white";
              }

              switch (state) {
                case 'answered':
                case 'correct':
                case 'wrong':
                  return "text-14 font-semibold text-white";
                default:
                  return "text-14 font-semibold text-text-light";
              }
            };
            
            return (
              <TouchableOpacity
                key={index}
                className={getButtonClassName()}
                onPress={() => onQuestionPress(index)}
              >
                <Text className={getTextClassName()}>
                  {index + 1}
                </Text>
              </TouchableOpacity>
            );
          })}
          </ScrollView>
        </View>
      </View>
    );
  }
);

QuestionNumbersBar.displayName = 'QuestionNumbersBar';

