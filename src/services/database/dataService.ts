/**
 * Unified Database Service
 * Provides consistent data access layer for all database operations
 * Maintains data consistency between answer_records and question_stats tables
 */

import { isAndroid } from '../../utils/platform';
import { <PERSON><PERSON>r<PERSON><PERSON>, errorHandler, validateAnswer } from '../error/errorHandler';
import { getDatabase } from './init';

export interface AnswerRecord {
  id?: number;
  questionId: number;
  volume: number;
  chapter: number;
  isCorrect: boolean;
  mode: 'practice' | 'exam';
  sessionId: string;
  selectedOption: string;
  correctOption: string;
  timeSpent: number;
  createdAt?: string;
}

export interface QuestionStats {
  questionId: number;
  volume: number;
  totalAttempts: number;
  correctAttempts: number;
  wrongAttempts: number;
  lastAttempted?: string;
  isBookmarked: boolean;
  note?: string;
}

export class DataService {
  private static instance: DataService;

  static getInstance(): DataService {
    if (!DataService.instance) {
      DataService.instance = new DataService();
    }
    return DataService.instance;
  }

  private getDB() {
    const db = getDatabase();
    if (!db) {
      throw new Error('Database not available');
    }
    return db;
  }

  /**
   * Check if a table exists in the database
   */
  private async tableExists(tableName: string): Promise<boolean> {
    try {
      const db = this.getDB();
      const result = await db.getFirstAsync(
        "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
        [tableName]
      );
      return !!result;
    } catch (error) {
      console.warn(`Failed to check if table ${tableName} exists:`, error);
      return false;
    }
  }

  /**
   * Record an answer and update all related statistics atomically
   * This ensures data consistency across tables
   */
  async recordAnswer(answer: AnswerRecord): Promise<void> {
    // Validate answer data first
    const validationErrors = validateAnswer(answer);
    if (validationErrors.length > 0) {
      throw errorHandler.createError(
        ErrorCode.INVALID_ANSWER_DATA,
        'Answer validation failed',
        validationErrors,
        { answer }
      );
    }

    const db = this.getDB();
    
    try {
      if (isAndroid) {
        // Android-specific implementation with better compatibility
        await this.recordAnswerAndroid(db, answer);
      } else {
        // iOS implementation with advanced features
        await this.recordAnswerIOS(db, answer);
      }
    } catch (error) {
      const appError = errorHandler.handleError(error as Error, { 
        operation: 'recordAnswer',
        answer 
      });
      
      if (appError.code === ErrorCode.UNKNOWN_ERROR) {
        appError.code = ErrorCode.ANSWER_SUBMISSION_FAILED;
      }
      
      throw appError;
    }
  }

  /**
   * Android-specific answer recording with maximum compatibility
   */
  private async recordAnswerAndroid(db: any, answer: AnswerRecord): Promise<void> {
    try {
      console.log('🤖 Starting Android answer recording...');
      
      // Check if answer_records table exists
      const answerTableExists = await this.tableExists('answer_records');
      if (!answerTableExists) {
        console.error('answer_records table does not exist');
        throw new Error('answer_records table not available');
      }

      // Try without transaction first for maximum compatibility
      try {
        // Use execAsync with string interpolation to avoid prepareAsync issues
        const insertSQL = `INSERT INTO answer_records (
          question_id, volume, chapter, is_correct, mode, session_id,
          selected_option, correct_option, time_spent
        ) VALUES (
          ${answer.questionId},
          ${answer.volume},
          ${answer.chapter},
          ${answer.isCorrect ? 1 : 0},
          '${answer.mode}',
          '${answer.sessionId}',
          '${answer.selectedOption}',
          '${answer.correctOption}',
          ${answer.timeSpent}
        );`;
        
        await db.execAsync(insertSQL);
        console.log('✅ Android answer record inserted');

        // Try to update question stats if table exists
        const statsTableExists = await this.tableExists('question_stats');
        if (statsTableExists) {
          try {
            await this.updateQuestionStatsAndroidSafe(db, {
              questionId: answer.questionId,
              volume: answer.volume,
              isCorrect: answer.isCorrect
            });
            console.log('✅ Android question stats updated');
          } catch (statsError) {
            console.warn('⚠️ Failed to update question stats, continuing:', statsError);
            // Continue - stats are not critical
          }
        } else {
          console.log('📋 question_stats table not available, skipping stats update');
        }

      } catch (error) {
        console.error('❌ Android answer recording with execAsync failed:', error);
        
        // Last resort - try with minimal functionality
        try {
          console.log('🔧 Attempting Android fallback recording...');
          await this.recordAnswerAndroidFallback(db, answer);
          console.log('✅ Android fallback recording succeeded');
        } catch (fallbackError) {
          console.error('❌ Android fallback recording also failed:', fallbackError);
          throw fallbackError;
        }
      }
    } catch (error) {
      console.error('❌ Complete Android answer recording failed:', error);
      throw error;
    }
  }

  /**
   * Android fallback recording - absolute minimal approach
   */
  private async recordAnswerAndroidFallback(db: any, answer: AnswerRecord): Promise<void> {
    // Try to just store the data without any prepared statements or complex queries
    const currentTime = new Date().toISOString();
    
    const insertSQL = `
      INSERT INTO answer_records (
        question_id, volume, chapter, is_correct, mode, session_id,
        selected_option, correct_option, time_spent, created_at
      ) VALUES (
        ${answer.questionId},
        ${answer.volume}, 
        ${answer.chapter},
        ${answer.isCorrect ? 1 : 0},
        '${answer.mode.replace(/'/g, "''")}',
        '${answer.sessionId.replace(/'/g, "''")}',
        '${answer.selectedOption.replace(/'/g, "''")}',
        '${answer.correctOption.replace(/'/g, "''")}',
        ${answer.timeSpent},
        '${currentTime}'
      );
    `;
    
    await db.execAsync(insertSQL);
  }

  /**
   * iOS-specific answer recording with full transaction support
   */
  private async recordAnswerIOS(db: any, answer: AnswerRecord): Promise<void> {
    // Use withTransactionAsync for proper transaction management on iOS
    await db.withTransactionAsync(async () => {
      // 1. Insert answer record
      await db.runAsync(
        `INSERT INTO answer_records (
          question_id, volume, chapter, is_correct, mode, session_id,
          selected_option, correct_option, time_spent
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          answer.questionId,
          answer.volume,
          answer.chapter,
          answer.isCorrect ? 1 : 0,
          answer.mode,
          answer.sessionId,
          answer.selectedOption,
          answer.correctOption,
          answer.timeSpent
        ]
      );

      // 2. Update question stats directly within this transaction
      await this.updateQuestionStatsInTransaction(db, {
        questionId: answer.questionId,
        volume: answer.volume,
        isCorrect: answer.isCorrect
      });
    });
  }

  /**
   * Update question statistics within an existing transaction (iOS)
   * This method doesn't start its own transaction - it must be called within one
   */
  private async updateQuestionStatsInTransaction(db: any, data: {
    questionId: number;
    volume: number;
    isCorrect: boolean;
  }): Promise<void> {
    try {
      // Try composite primary key approach first (question_id, volume)
      await db.runAsync(
        `INSERT INTO question_stats 
         (question_id, volume, total_attempts, correct_attempts, wrong_attempts, last_attempted)
         VALUES (?, ?, 1, ?, ?, datetime('now'))
         ON CONFLICT(question_id, volume) DO UPDATE SET
           total_attempts = total_attempts + 1,
           correct_attempts = correct_attempts + ?,
           wrong_attempts = wrong_attempts + ?,
           last_attempted = datetime('now')`,
        [
          data.questionId,
          data.volume,
          data.isCorrect ? 1 : 0,
          data.isCorrect ? 0 : 1,
          data.isCorrect ? 1 : 0,
          data.isCorrect ? 0 : 1,
        ]
      );
    } catch {
      // Fallback to single primary key approach (question_id only)
      await db.runAsync(
        `INSERT INTO question_stats 
         (question_id, volume, total_attempts, correct_attempts, wrong_attempts, last_attempted)
         VALUES (?, ?, 1, ?, ?, datetime('now'))
         ON CONFLICT(question_id) DO UPDATE SET
           volume = ?,
           total_attempts = total_attempts + 1,
           correct_attempts = correct_attempts + ?,
           wrong_attempts = wrong_attempts + ?,
           last_attempted = datetime('now')`,
        [
          data.questionId,
          data.volume,
          data.isCorrect ? 1 : 0,
          data.isCorrect ? 0 : 1,
          data.volume,
          data.isCorrect ? 1 : 0,
          data.isCorrect ? 0 : 1,
        ]
      );
    }
  }

  /**
   * Android-safe question stats update avoiding prepareAsync
   */
  private async updateQuestionStatsAndroidSafe(db: any, data: {
    questionId: number;
    volume: number;
    isCorrect: boolean;
  }): Promise<void> {
    try {
      console.log('🤖 Updating Android question stats...');
      
      // Use execAsync with string interpolation to avoid prepareAsync
      const currentTime = new Date().toISOString();
      
      // First, try to check if record exists
      let existing = null;
      try {
        const checkSQL = `SELECT * FROM question_stats WHERE question_id = ${data.questionId} AND volume = ${data.volume};`;
        const result = await db.getAllAsync(checkSQL);
        existing = result && result.length > 0 ? result[0] : null;
      } catch (error) {
        console.warn('Failed to check existing stats, assuming new record:', error);
      }

      if (existing) {
        // Update existing record
        const updateSQL = `
          UPDATE question_stats SET
            total_attempts = total_attempts + 1,
            correct_attempts = correct_attempts + ${data.isCorrect ? 1 : 0},
            wrong_attempts = wrong_attempts + ${data.isCorrect ? 0 : 1},
            last_attempted = '${currentTime}'
          WHERE question_id = ${data.questionId} AND volume = ${data.volume};
        `;
        await db.execAsync(updateSQL);
      } else {
        // Insert new record
        const insertSQL = `
          INSERT INTO question_stats 
            (question_id, volume, total_attempts, correct_attempts, wrong_attempts, last_attempted)
          VALUES (
            ${data.questionId},
            ${data.volume},
            1,
            ${data.isCorrect ? 1 : 0},
            ${data.isCorrect ? 0 : 1},
            '${currentTime}'
          );
        `;
        await db.execAsync(insertSQL);
      }
    } catch (error) {
      console.error('❌ Android question stats update failed:', error);
      throw error;
    }
  }

  /**
   * Android-specific question stats update using separate SELECT/INSERT/UPDATE
   * More compatible approach that avoids ON CONFLICT issues
   */
  private async updateQuestionStatsAndroid(db: any, data: {
    questionId: number;
    volume: number;
    isCorrect: boolean;
  }): Promise<void> {
    // Use the safer version
    return this.updateQuestionStatsAndroidSafe(db, data);
  }

  /**
   * Get seen question IDs for given volumes
   * Uses answer_records as single source of truth
   */
  async getSeenQuestionIds(volumes: number[]): Promise<Set<number>> {
    try {
      // Check if answer_records table exists
      const tableExists = await this.tableExists('answer_records');
      if (!tableExists) {
        console.warn('answer_records table does not exist, returning empty set');
        return new Set();
      }

      const db = this.getDB();
      
      if (isAndroid) {
        // Use string interpolation for Android to avoid prepareAsync issues
        const volumeList = volumes.join(',');
        const sql = `SELECT DISTINCT question_id FROM answer_records WHERE volume IN (${volumeList});`;
        const result = await db.getAllAsync(sql) as { question_id: number }[];
        return new Set(result.map(row => row.question_id));
      } else {
        // Use prepared statements for iOS
        const placeholders = volumes.map(() => '?').join(',');
        const result = await db.getAllAsync(
          `SELECT DISTINCT question_id FROM answer_records
           WHERE volume IN (${placeholders})`,
          volumes
        ) as { question_id: number }[];
        return new Set(result.map(row => row.question_id));
      }
    } catch (error) {
      console.error('Failed to get seen question IDs:', error);
      return new Set(); // Return empty set instead of throwing
    }
  }

  /**
   * Get wrong question IDs (questions answered incorrectly and never correctly)
   */
  async getWrongQuestionIds(volumes: number[]): Promise<Set<number>> {
    try {
      // Check if answer_records table exists
      const tableExists = await this.tableExists('answer_records');
      if (!tableExists) {
        console.warn('answer_records table does not exist, returning empty set');
        return new Set();
      }

      const db = this.getDB();
      
      if (isAndroid) {
        // Use string interpolation for Android
        const volumeList = volumes.join(',');
        const sql = `
          SELECT question_id
          FROM answer_records 
          WHERE volume IN (${volumeList})
          GROUP BY question_id 
          HAVING SUM(CASE WHEN is_correct = 1 THEN 1 ELSE 0 END) = 0 
          AND SUM(CASE WHEN is_correct = 0 THEN 1 ELSE 0 END) > 0;
        `;
        const result = await db.getAllAsync(sql) as { question_id: number }[];
        return new Set(result.map(row => row.question_id));
      } else {
        // Use prepared statements for iOS
        const placeholders = volumes.map(() => '?').join(',');
        const result = await db.getAllAsync(
          `SELECT question_id
           FROM answer_records 
           WHERE volume IN (${placeholders})
           GROUP BY question_id 
           HAVING SUM(CASE WHEN is_correct = 1 THEN 1 ELSE 0 END) = 0 
           AND SUM(CASE WHEN is_correct = 0 THEN 1 ELSE 0 END) > 0`,
          volumes
        ) as { question_id: number }[];
        return new Set(result.map(row => row.question_id));
      }
    } catch (error) {
      console.error('Failed to get wrong question IDs:', error);
      return new Set(); // Return empty set instead of throwing
    }
  }

  /**
   * Get bookmarked question IDs
   */
  async getBookmarkedQuestionIds(volumes: number[]): Promise<Set<number>> {
    try {
      // Check if question_stats table exists
      const tableExists = await this.tableExists('question_stats');
      if (!tableExists) {
        console.warn('question_stats table does not exist, returning empty set');
        return new Set();
      }

      const db = this.getDB();
      
      if (isAndroid) {
        // Use string interpolation for Android
        const volumeList = volumes.join(',');
        const sql = `SELECT DISTINCT question_id FROM question_stats WHERE volume IN (${volumeList}) AND is_bookmarked = 1;`;
        const result = await db.getAllAsync(sql) as { question_id: number }[];
        return new Set(result.map(row => row.question_id));
      } else {
        // Use prepared statements for iOS
        const placeholders = volumes.map(() => '?').join(',');
        const result = await db.getAllAsync(
          `SELECT DISTINCT question_id FROM question_stats
           WHERE volume IN (${placeholders}) AND is_bookmarked = 1`,
          volumes
        ) as { question_id: number }[];
        return new Set(result.map(row => row.question_id));
      }
    } catch (error) {
      console.error('Failed to get bookmarked question IDs:', error);
      return new Set(); // Return empty set instead of throwing
    }
  }

  /**
   * Toggle bookmark status for a question
   */
  async toggleBookmark(questionId: number, volume: number): Promise<boolean> {
    try {
      if (isAndroid) {
        return await this.toggleBookmarkAndroid(questionId, volume);
      } else {
        return await this.toggleBookmarkIOS(questionId, volume);
      }
    } catch (error) {
      console.error('Failed to toggle bookmark:', error);
      throw error;
    }
  }

  /**
   * Android-compatible bookmark toggle
   */
  private async toggleBookmarkAndroid(questionId: number, volume: number): Promise<boolean> {
    const db = this.getDB();
    
    // Check if record exists
    const existing = await db.getFirstAsync(
      'SELECT is_bookmarked FROM question_stats WHERE question_id = ? AND volume = ?',
      [questionId, volume]
    ) as { is_bookmarked: number } | null;

    if (existing) {
      // Update existing record
      const newBookmarkStatus = existing.is_bookmarked === 1 ? 0 : 1;
      await db.runAsync(
        'UPDATE question_stats SET is_bookmarked = ? WHERE question_id = ? AND volume = ?',
        [newBookmarkStatus, questionId, volume]
      );
      return newBookmarkStatus === 1;
    } else {
      // Insert new record with bookmark set to true
      await db.runAsync(
        `INSERT INTO question_stats (question_id, volume, total_attempts, correct_attempts, wrong_attempts, is_bookmarked)
         VALUES (?, ?, 0, 0, 0, 1)`,
        [questionId, volume]
      );
      return true;
    }
  }

  /**
   * iOS bookmark toggle with ON CONFLICT support
   */
  private async toggleBookmarkIOS(questionId: number, volume: number): Promise<boolean> {
    const db = this.getDB();
    
    // First, ensure the question stats record exists
    await db.runAsync(
      `INSERT INTO question_stats (question_id, volume, total_attempts, correct_attempts, wrong_attempts, is_bookmarked)
       VALUES (?, ?, 0, 0, 0, 0)
       ON CONFLICT(question_id, volume) DO NOTHING`,
      [questionId, volume]
    );

    // Toggle bookmark status
    await db.runAsync(
      `UPDATE question_stats 
       SET is_bookmarked = CASE WHEN is_bookmarked = 1 THEN 0 ELSE 1 END
       WHERE question_id = ? AND volume = ?`,
      [questionId, volume]
    );

    // Get the new bookmark status
    const bookmarkStatus = await db.getFirstAsync(
      `SELECT is_bookmarked FROM question_stats WHERE question_id = ? AND volume = ?`,
      [questionId, volume]
    ) as { is_bookmarked: number } | null;

    return bookmarkStatus?.is_bookmarked === 1;
  }

  /**
   * Get question statistics
   */
  async getQuestionStats(questionId: number, volume: number): Promise<QuestionStats | null> {
    try {
      const result = await this.getDB().getFirstAsync(
        `SELECT * FROM question_stats WHERE question_id = ? AND volume = ?`,
        [questionId, volume]
      ) as any;

      if (!result) return null;

      return {
        questionId: result.question_id,
        volume: result.volume,
        totalAttempts: result.total_attempts,
        correctAttempts: result.correct_attempts,
        wrongAttempts: result.wrong_attempts,
        lastAttempted: result.last_attempted,
        isBookmarked: result.is_bookmarked === 1,
        note: result.note
      };
    } catch (error) {
      console.error('Failed to get question stats:', error);
      throw error;
    }
  }

  /**
   * Update question note
   */
  async updateQuestionNote(questionId: number, volume: number, note: string): Promise<void> {
    try {
      if (isAndroid) {
        await this.updateQuestionNoteAndroid(questionId, volume, note);
      } else {
        await this.updateQuestionNoteIOS(questionId, volume, note);
      }
    } catch (error) {
      console.error('Failed to update question note:', error);
      throw error;
    }
  }

  /**
   * Android-compatible question note update
   */
  private async updateQuestionNoteAndroid(questionId: number, volume: number, note: string): Promise<void> {
    const db = this.getDB();
    
    // Check if record exists
    const existing = await db.getFirstAsync(
      'SELECT * FROM question_stats WHERE question_id = ? AND volume = ?',
      [questionId, volume]
    );

    if (existing) {
      // Update existing record
      await db.runAsync(
        'UPDATE question_stats SET note = ? WHERE question_id = ? AND volume = ?',
        [note, questionId, volume]
      );
    } else {
      // Insert new record
      await db.runAsync(
        `INSERT INTO question_stats (question_id, volume, total_attempts, correct_attempts, wrong_attempts, note)
         VALUES (?, ?, 0, 0, 0, ?)`,
        [questionId, volume, note]
      );
    }
  }

  /**
   * iOS question note update with ON CONFLICT support
   */
  private async updateQuestionNoteIOS(questionId: number, volume: number, note: string): Promise<void> {
    // Ensure record exists first
    await this.getDB().runAsync(
      `INSERT INTO question_stats (question_id, volume, total_attempts, correct_attempts, wrong_attempts, note)
       VALUES (?, ?, 0, 0, 0, ?)
       ON CONFLICT(question_id, volume) DO UPDATE SET note = ?`,
      [questionId, volume, note, note]
    );
  }
}

export const dataService = DataService.getInstance();