/**
 * Platform detection utilities for React Native
 * Provides consistent platform checking across the app
 */

import { Platform } from 'react-native';

export const isAndroid = Platform.OS === 'android';
export const isIOS = Platform.OS === 'ios';
export const isWeb = Platform.OS === 'web';

/**
 * Get platform-specific database configuration
 */
export const getDatabaseConfig = () => {
  if (isAndroid) {
    return {
      // Android-specific config - ultra-conservative settings for maximum compatibility
      enableWAL: false, // WAL mode can be problematic on some Android devices
      enableForeignKeys: false, // Disable to avoid locking issues
      busyTimeout: 10000, // 10 seconds - longer timeout for problematic devices
      journalMode: 'DELETE', // Most compatible mode on Android
      synchronous: 'NORMAL', // Balance between safety and performance
      pageSize: 1024, // Smaller page size for better compatibility
      cacheSize: 1000, // Smaller cache to reduce memory issues
      useStringInterpolation: true, // Use string interpolation instead of prepared statements
      avoidTransactions: true // Avoid transactions when possible
    };
  } else if (isIOS) {
    return {
      // iOS-specific config - can use more advanced features
      enableWAL: true,
      enableForeignKeys: true,
      busyTimeout: 3000,
      journalMode: 'WAL',
      synchronous: 'NORMAL',
      pageSize: 4096,
      cacheSize: 2000
    };
  } else {
    return {
      // Web/default config
      enableWAL: false,
      enableForeignKeys: true,
      busyTimeout: 5000,
      journalMode: 'DELETE',
      synchronous: 'NORMAL',
      pageSize: 4096,
      cacheSize: 1000
    };
  }
};

/**
 * Check if the current platform supports advanced SQLite features
 */
export const supportsAdvancedSQLite = () => {
  return isIOS; // iOS generally has better SQLite support
};

/**
 * Get platform-appropriate retry configuration
 */
export const getRetryConfig = () => {
  if (isAndroid) {
    return {
      maxRetries: 5, // More retries for Android
      initialDelay: 1000, // 1 second
      backoffMultiplier: 1.5,
      maxDelay: 10000 // 10 seconds
    };
  } else {
    return {
      maxRetries: 3,
      initialDelay: 500,
      backoffMultiplier: 2,
      maxDelay: 5000
    };
  }
};
