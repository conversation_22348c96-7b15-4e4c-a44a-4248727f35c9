/**
 * Test utility to create sample wrong questions data for testing
 */
import { getDatabase } from '../services/database/init';

export async function createSampleWrongQuestionsData() {
  try {
    console.log('Creating sample wrong questions data...');
    const db = getDatabase();
    
    // Sample data for volume 1 questions with wrong attempts
    const sampleData = [
      { questionId: 1, volume: 1, wrongAttempts: 5, correctAttempts: 2, totalAttempts: 7 },
      { questionId: 3, volume: 1, wrongAttempts: 4, correctAttempts: 3, totalAttempts: 7 },
      { questionId: 5, volume: 1, wrongAttempts: 3, correctAttempts: 4, totalAttempts: 7 },
      { questionId: 7, volume: 1, wrongAttempts: 3, correctAttempts: 1, totalAttempts: 4 },
      { questionId: 9, volume: 1, wrongAttempts: 2, correctAttempts: 5, totalAttempts: 7 },
    ];
    
    // Insert or update question stats
    for (const data of sampleData) {
      await db.runAsync(
        `INSERT OR REPLACE INTO question_stats 
         (question_id, volume, total_attempts, correct_attempts, wrong_attempts, last_attempted)
         VALUES (?, ?, ?, ?, ?, datetime('now'))`,
        [data.questionId, data.volume, data.totalAttempts, data.correctAttempts, data.wrongAttempts]
      );
    }
    
    console.log('Sample wrong questions data created successfully!');
    console.log('You should now see wrong questions in Volume 1');
    
    return sampleData;
  } catch (error) {
    console.error('Failed to create sample data:', error);
    throw error;
  }
}

export async function clearSampleWrongQuestionsData() {
  try {
    console.log('Clearing sample wrong questions data...');
    const db = getDatabase();
    
    // Clear the sample data
    await db.runAsync('DELETE FROM question_stats WHERE volume = 1 AND question_id IN (1, 3, 5, 7, 9)');
    
    console.log('Sample wrong questions data cleared!');
  } catch (error) {
    console.error('Failed to clear sample data:', error);
    throw error;
  }
}

// Export for easy testing in development
if (__DEV__) {
  (global as any).createSampleWrongQuestionsData = createSampleWrongQuestionsData;
  (global as any).clearSampleWrongQuestionsData = clearSampleWrongQuestionsData;
}
