/**
 * Test utility to verify most wrong questions functionality
 */
import { QuestionStatsService } from '../services/database/questionStatsService';

export async function testMostWrongQuestions(volume: number = 1) {
  try {
    console.log(`\n=== Testing Most Wrong Questions for Volume ${volume} ===`);
    
    const questionStatsService = QuestionStatsService.getInstance();
    const mostWrongQuestions = await questionStatsService.getMostWrongQuestions(volume, 5);
    
    console.log(`Found ${mostWrongQuestions.length} wrong questions:`);
    
    mostWrongQuestions.forEach((question, index) => {
      console.log(`${index + 1}. Question ${question.questionId}:`);
      console.log(`   - Wrong attempts: ${question.wrongAttempts}`);
      console.log(`   - Total attempts: ${question.totalAttempts}`);
      console.log(`   - Accuracy: ${question.accuracy.toFixed(1)}%`);
      console.log(`   - Last attempted: ${question.lastAttempted || 'Never'}`);
    });
    
    if (mostWrongQuestions.length === 0) {
      console.log('No wrong questions found for this volume. This could mean:');
      console.log('1. User hasn\'t answered any questions incorrectly yet');
      console.log('2. No questions have been attempted in this volume');
      console.log('3. All questions were answered correctly');
    }
    
    console.log('=== Test Complete ===\n');
    
    return mostWrongQuestions;
  } catch (error) {
    console.error('Test failed:', error);
    throw error;
  }
}

// Export for easy testing in development
if (__DEV__) {
  (global as any).testMostWrongQuestions = testMostWrongQuestions;
}
