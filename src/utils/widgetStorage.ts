import { ExtensionStorage } from '@bacons/apple-targets';
import WidgetKit from 'react-native-widgetkit';

const storage = new ExtensionStorage('group.com.248labs.macaudrivesaver');

export interface WidgetData {
  totalAvailableQuestions: number;
  correctAnswers: number;
  totalAnswered: number;
  examDate: string;
}

export const updateWidgetData = (data: WidgetData) => {
  try {
    // Calculate derived values for the widget
    const accuracy = data.totalAnswered > 0 ? (data.correctAnswers / data.totalAnswered) * 100 : 0;

    // Use the same calculation as dashboard: mastered questions / total questions seen
    // But for widget display, we'll show progress against all 647 questions
    const totalAllQuestions = 647; // Total questions across all volumes (176+66+150+139+116)
    const studyProgress = Math.round((data.correctAnswers / totalAllQuestions) * 100);

    // Calculate days remaining if exam date is set
    let daysRemaining: number | null = null;
    if (data.examDate && data.examDate !== '未設定') {
      try {
        const examDate = new Date(data.examDate);
        const today = new Date();
        today.setHours(0, 0, 0, 0); // Reset time to start of day
        examDate.setHours(0, 0, 0, 0); // Reset time to start of day

        const diffTime = examDate.getTime() - today.getTime();
        daysRemaining = Math.ceil(diffTime / (1000 * 60 * 60 * 24)); // Convert to days
      } catch (dateError) {
        console.warn('Failed to parse exam date:', data.examDate, dateError);
        daysRemaining = null;
      }
    }

    // Create widget data object
    const widgetData = {
      examDate: data.examDate,
      daysRemaining: daysRemaining,
      studyProgress: studyProgress,
      masteredQuestions: data.correctAnswers,
      totalQuestions: totalAllQuestions, // Use total available questions (647)
      accuracy: accuracy
    };

    // Store as JSON string under 'widgetData' key (as expected by the widget)
    const jsonString = JSON.stringify(widgetData);
    storage.set('widgetData', jsonString);
    console.log('Widget data updated successfully:', widgetData);
    console.log('Widget data JSON string:', jsonString);

    // Verify the data was stored
    const storedData = storage.get('widgetData');
    console.log('Widget data verification - stored:', storedData);

    // Reload the widget to show updated data immediately
    try {
      WidgetKit.reloadAllTimelines();
      console.log('Widget timeline reloaded successfully');
    } catch (error) {
      console.warn('Failed to reload widget timeline:', error);
    }
  } catch (error) {
    console.error('Failed to update widget data:', error);
  }
};

export const getWidgetData = (): WidgetData => {
  try {
    const widgetDataString = storage.get('widgetData') as string;
    if (widgetDataString) {
      const widgetData = JSON.parse(widgetDataString);
      return {
        totalAvailableQuestions: 1000, // This would need to be stored separately if needed
        correctAnswers: widgetData.masteredQuestions || 0,
        totalAnswered: widgetData.totalQuestions || 0,
        examDate: widgetData.examDate || '未設定',
      };
    }

    // Return default values if no data found
    return {
      totalAvailableQuestions: 1000,
      correctAnswers: 0,
      totalAnswered: 0,
      examDate: '未設定',
    };
  } catch (error) {
    console.error('Failed to get widget data:', error);
    return {
      totalAvailableQuestions: 1000,
      correctAnswers: 0,
      totalAnswered: 0,
      examDate: '未設定',
    };
  }
};
