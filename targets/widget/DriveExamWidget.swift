import WidgetKit
import SwiftUI

// 簡化的 Widget Entry
struct SimpleEntry: TimelineEntry {
    let date: Date
    let examDate: String?
    let daysRemaining: Int?
    let studyProgress: Int
    let masteredQuestions: Int
    let totalQuestions: Int
    let accuracy: Double
}

// 簡化的 Widget Provider
struct SimpleProvider: TimelineProvider {
    private let appGroupIdentifier = "group.com.248labs.macaudrivesaver"
    
    func placeholder(in context: Context) -> SimpleEntry {
        SimpleEntry(
            date: Date(),
            examDate: nil,
            daysRemaining: nil,
            studyProgress: 0,
            masteredQuestions: 0,
            totalQuestions: 0,
            accuracy: 0.0
        )
    }

    func getSnapshot(in context: Context, completion: @escaping (SimpleEntry) -> ()) {
        let entry = loadWidgetData()
        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        let currentDate = Date()
        let entry = loadWidgetData()

        // Update every 1 minute for more responsive progress tracking
        let nextUpdateDate = Calendar.current.date(byAdding: .minute, value: 1, to: currentDate)!
        let timeline = Timeline(entries: [entry], policy: .after(nextUpdateDate))

        completion(timeline)
    }
    
    private func loadWidgetData() -> SimpleEntry {
        // 嘗試從 UserDefaults 讀取數據
        if let userDefaults = UserDefaults(suiteName: appGroupIdentifier),
           let widgetDataString = userDefaults.string(forKey: "widgetData"),
           let data = widgetDataString.data(using: .utf8) {
            
            do {
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                    let examDate = json["examDate"] as? String
                    let daysRemaining = json["daysRemaining"] as? Int
                    let studyProgress = json["studyProgress"] as? Int ?? 0
                    let masteredQuestions = json["masteredQuestions"] as? Int ?? 0
                    let totalQuestions = json["totalQuestions"] as? Int ?? 0
                    let accuracy = json["accuracy"] as? Double ?? 0.0
                    
                    return SimpleEntry(
                        date: Date(),
                        examDate: examDate,
                        daysRemaining: daysRemaining,
                        studyProgress: studyProgress,
                        masteredQuestions: masteredQuestions,
                        totalQuestions: totalQuestions,
                        accuracy: accuracy
                    )
                }
            } catch {
                print("解析 JSON 失敗: \(error)")
            }
        }
        
        // 返回預設值
        return SimpleEntry(
            date: Date(),
            examDate: nil,
            daysRemaining: nil,
            studyProgress: 0,
            masteredQuestions: 0,
            totalQuestions: 0,
            accuracy: 0.0
        )
    }
}

// 簡化的 Widget 視圖
struct SimpleWidgetView: View {
    var entry: SimpleProvider.Entry
    @Environment(\.widgetFamily) var family
    
    var body: some View {
        switch family {
        case .systemSmall:
            SmallWidgetView(entry: entry)
        case .systemMedium:
            MediumWidgetView(entry: entry)
        default:
            SmallWidgetView(entry: entry)
        }
    }
}

struct SmallWidgetView: View {
    var entry: SimpleEntry
    
    var body: some View {
        VStack(spacing: 8) {
            HStack {
                Text("駕照考試")
                    .font(.system(size: 12, weight: .bold))
                    .foregroundColor(.primary)
                Spacer()
            }
            
            Spacer()
            
            if let daysRemaining = entry.daysRemaining {
                VStack(spacing: 4) {
                    Text(daysRemaining < 0 ? "考試已過期" : daysRemaining == 0 ? "今天考試!" : "還有")
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(.secondary)
                    
                    Text("\(abs(daysRemaining))")
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(daysRemaining <= 7 ? .red : daysRemaining <= 30 ? .orange : .green)
                    
                    if daysRemaining > 0 {
                        Text("天")
                            .font(.system(size: 10, weight: .medium))
                            .foregroundColor(.secondary)
                    }
                }
            } else {
                VStack(spacing: 4) {
                    Text("未設定")
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(.secondary)
                    Text("考試日期")
                        .font(.system(size: 14, weight: .bold))
                        .foregroundColor(.primary)
                }
            }
            
            Spacer()
            
            HStack {
                Text("進度")
                    .font(.system(size: 10, weight: .medium))
                    .foregroundColor(.secondary)
                Spacer()
                Text("\(entry.studyProgress)%")
                    .font(.system(size: 12, weight: .bold))
                    .foregroundColor(entry.studyProgress >= 80 ? .green : entry.studyProgress >= 60 ? .orange : .red)
            }
        }
        .padding(16)
        .background(Color(.systemBackground))
    }
}

struct MediumWidgetView: View {
    var entry: SimpleEntry
    
    var body: some View {
        HStack(spacing: 16) {
            // 左側：考試倒數
            VStack(spacing: 8) {
                HStack {
                    Text("駕照考試")
                        .font(.system(size: 14, weight: .bold))
                        .foregroundColor(.primary)
                    Spacer()
                }
                
                if let daysRemaining = entry.daysRemaining {
                    VStack(spacing: 6) {
                        Text(daysRemaining < 0 ? "考試已過期" : daysRemaining == 0 ? "今天考試!" : "還有")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.secondary)
                        
                        Text("\(abs(daysRemaining))")
                            .font(.system(size: 32, weight: .bold))
                            .foregroundColor(daysRemaining <= 7 ? .red : daysRemaining <= 30 ? .orange : .green)
                        
                        if daysRemaining > 0 {
                            Text("天")
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(.secondary)
                        }
                    }
                } else {
                    VStack(spacing: 6) {
                        Text("未設定考試日期")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                        
                        Image(systemName: "calendar.badge.plus")
                            .font(.system(size: 24))
                            .foregroundColor(.blue)
                    }
                }
                
                Spacer()
            }
            
            Rectangle()
                .fill(Color(.separator))
                .frame(width: 1)
            
            // 右側：學習進度
            VStack(spacing: 8) {
                HStack {
                    Text("學習進度")
                        .font(.system(size: 14, weight: .bold))
                        .foregroundColor(.primary)
                    Spacer()
                }
                
                // 簡化的進度顯示
                VStack(spacing: 8) {
                    Text("\(entry.studyProgress)%")
                        .font(.system(size: 32, weight: .bold))
                        .foregroundColor(entry.studyProgress >= 80 ? .green : entry.studyProgress >= 60 ? .orange : .red)
                    
                    VStack(spacing: 2) {
                        Text("\(entry.masteredQuestions) / \(entry.totalQuestions)")
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(.primary)
                        
                        Text("已掌握題目")
                            .font(.system(size: 10, weight: .medium))
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
            }
        }
        .padding(16)
        .background(Color(.systemBackground))
        .containerBackground(for: .widget) {
            Color(.systemBackground)
        }
    }
}

// 主要的 Widget 配置
@main
struct DriveExamWidgetSimple: Widget {
    let kind: String = "DriveExamWidgetSimple"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: SimpleProvider()) { entry in
            SimpleWidgetView(entry: entry)
        }
        .configurationDisplayName("駕照考試助手")
        .description("顯示考試倒數天數和學習進度")
        .supportedFamilies([.systemSmall, .systemMedium])
    }
}

// MARK: - Previews
struct DriveExamWidget_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // Small Widget Preview
            SimpleWidgetView(entry: SimpleEntry(
                date: Date(),
                examDate: "2024-12-15",
                daysRemaining: 25,
                studyProgress: 65,
                masteredQuestions: 420,
                totalQuestions: 647,
                accuracy: 85.5
            ))
            .previewContext(WidgetPreviewContext(family: .systemSmall))
            .previewDisplayName("Small Widget")

            // Medium Widget Preview
            SimpleWidgetView(entry: SimpleEntry(
                date: Date(),
                examDate: "2024-12-15",
                daysRemaining: 25,
                studyProgress: 65,
                masteredQuestions: 420,
                totalQuestions: 647,
                accuracy: 85.5
            ))
            .previewContext(WidgetPreviewContext(family: .systemMedium))
            .previewDisplayName("Medium Widget")

            // Empty State Preview
            SimpleWidgetView(entry: SimpleEntry(
                date: Date(),
                examDate: nil,
                daysRemaining: nil,
                studyProgress: 0,
                masteredQuestions: 0,
                totalQuestions: 647,
                accuracy: 0.0
            ))
            .previewContext(WidgetPreviewContext(family: .systemSmall))
            .previewDisplayName("Empty State")
        }
    }
}