/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./app/**/*.{js,jsx,ts,tsx}",
    "./components/**/*.{js,jsx,ts,tsx}",
    "./src/**/*.{js,jsx,ts,tsx}",
  ],
  presets: [require('nativewind/preset')],
  theme: {
    extend: {
      colors: {
        // Custom colors from your app
        'theme': 'rgba(163, 196, 201, 1)',
        'theme-text': 'rgb(68, 169, 184)',
        'theme-disabled': 'rgb(163, 163, 163)',
        'primary': 'rgb(177, 197, 218)',
        'success': 'rgba(52, 199, 89, 1)',
        'error': 'rgba(255, 59, 48, 1)',
        'warning': 'rgba(255, 149, 0, 1)',
        'background': 'rgb(241, 237, 229)',
        'card': 'rgba(255, 255, 255, 1)',
        'card-disabled': 'rgba(140, 140, 140, 0.1)',
        'accent': 'rgba(163, 196, 201, 1)',
        'accent-dark': 'rgba(123, 165, 171, 1)',
        'text': 'rgba(44, 44, 46, 1)',
        'text-light': 'rgba(109, 109, 112, 1)',
        'text-secondary': 'rgba(142, 142, 147, 1)',
        'border': 'rgba(229, 229, 234, 1)',
        'shadow': 'rgba(0, 0, 0, 0.2)',
      },
      spacing: {
        // Custom spacing values to match original StyleSheet
        '3': '12px', // for py-3 (paddingVertical: 12)
        '1.5': '6px', // for pt-1.5 (paddingTop: 6)
        '25': '100px', // for timer container width
        '5.5': '22px', // for checkbox size
      },
      minHeight: {
        // Custom min-height values to match original StyleSheet
        '44': '44px', // for medium button
        '52': '52px', // for large button
      },
      fontSize: {
        // Custom font sizes to match original StyleSheet
        'base': '16px', // fix text-base not working issue
        '10': '10px', // for small labels
        '11': '11px', // for small labels
        '12': '12px', // for small tags
        '13': '13px', // for small tags
        '14': '14px', // for details text
        '15': '15px', // for custom 15px font size
        '16': '16px', // for standard text
        '17': '17px', // for button text
        '18': '18px', // for score values
        '20': '20px', // for card titles
        '22': '22px', // for section titles
        '26': '26px', // for section titles
        '28': '28px', // for result titles
        '32': '32px', // for large titles
        '40': '40px', // for emoji text
        '48': '48px', // for large percentage displays
        '60': '60px', // for result icons
      },
    },
  },
  plugins: [],
}

