{"expo": {"name": "澳門考車神器", "slug": "macau-drive-exam", "owner": "248-labs", "version": "0.4.0", "orientation": "portrait", "icon": "./assets/images/icons/icon.png", "scheme": "macaudriveexam", "userInterfaceStyle": "automatic", "newArchEnabled": true, "locales": {"en": {"displayName": "Macau Drive Exam Saver"}, "zh-Hant": {"displayName": "澳門考車神器"}}, "ios": {"supportsTablet": false, "bundleIdentifier": "com.248labs.macaudrivesaver", "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "CFBundleAllowMixedLocalizations": true}, "entitlements": {"com.apple.security.application-groups": ["group.com.248labs.macaudrivesaver"]}}, "android": {"edgeToEdgeEnabled": true, "package": "com.x248labs.macaudrivesaver"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "@bacons/apple-targets", ["expo-splash-screen", {"image": "./assets/images/icons/icon_rounded.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#F0EDE6"}], ["expo-sqlite", {"enableFTS": false, "useSQLCipher": false, "android": {"enableFTS": false, "useSQLCipher": false, "enableWAL": false, "enableAsyncDatabase": false}, "ios": {"enableFTS": true, "useSQLCipher": false, "customBuildFlags": ["-DSQLITE_ENABLE_DBSTAT_VTAB=1 -DSQLITE_ENABLE_SNAPSHOT=1"]}}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "4190d37b-d565-4059-88aa-6c3a9a5639d6"}}}}