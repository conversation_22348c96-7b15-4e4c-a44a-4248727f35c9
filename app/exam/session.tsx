import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { router, useNavigation } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useLayoutEffect, useRef, useState } from 'react';
import { Alert, Platform, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { Button, ConfirmationModal } from '../../src/components/common';
import { OptionsList, QuestionCard, QuestionNumbersBar } from '../../src/components/question';
import { useRealtimeAnswer } from '../../src/hooks/useRealtimeAnswer';
import { useExamStore } from '../../src/store/useExamStore';
import { COLORS, DEBUG } from '../../src/utils/constants';

export default function ExamSessionScreen() {
  const navigation = useNavigation();
  const showQuestionNumbers = true; // Always show question numbers in exam mode
  const [scrollViewWidth, setScrollViewWidth] = useState(0);
  const [currentScrollX, setCurrentScrollX] = useState(0);
  const [isManualScrolling, setIsManualScrolling] = useState(false);
  const [showExitModal, setShowExitModal] = useState(false);
  const [showSubmitModal, setShowSubmitModal] = useState(false);
  const questionNumbersScrollRef = useRef<ScrollView>(null);
  const manualScrollTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const {
    session,
    currentQuestion,
    selectedAnswer,
    stats,
    isActive,
    isAutoSubmitted,
    selectAnswer,
    nextQuestion,
    previousQuestion,
    jumpToQuestion,
    submitExam,
    reset, // Add reset function
  } = useExamStore();

  const { recordAnswer } = useRealtimeAnswer();
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (!session && !isSubmitting) {
      router.dismissTo('/');
    }
  }, [session, isSubmitting]);

  // Handle auto-submission navigation
  useEffect(() => {
    if (isAutoSubmitted && !isActive && session?.endTime) {
      console.log('Exam auto-submitted due to time limit');
      router.replace('/exam/result');
    }
  }, [isAutoSubmitted, isActive, session?.endTime]);

  // Cleanup timer when component unmounts
  useEffect(() => {
    return () => {
      // If user navigates away while exam is active, clean up
      const examStore = useExamStore.getState();
      if (examStore.isActive && examStore.timerId) {
        reset(); // This will clear the timer and reset the state
      }
    };
  }, [reset]);

  // Auto-scroll to current question in question numbers bar (intelligent scrolling)
  useEffect(() => {
    if (showQuestionNumbers && questionNumbersScrollRef.current && session && scrollViewWidth > 0 && !isManualScrolling) {
      const questionWidth = 36; // Width of each question number button
      const questionGap = 8; // Gap between buttons
      const itemWidth = questionWidth + questionGap;
      const currentQuestionPosition = session.currentIndex * itemWidth;

      // Calculate visible area with buffer
      const buffer = itemWidth * 1.5; // Show 1.5 questions buffer on each side
      const visibleStart = currentScrollX;
      const visibleEnd = currentScrollX + scrollViewWidth;

      // Check if current question is outside visible area (with buffer)
      const questionStart = currentQuestionPosition;
      const questionEnd = currentQuestionPosition + questionWidth;

      const needsScrolling = questionStart < visibleStart + buffer || questionEnd > visibleEnd - buffer;

      if (needsScrolling) {
        // Center the current question in the viewport
        const scrollPosition = Math.max(0, currentQuestionPosition - (scrollViewWidth / 2) + (questionWidth / 2));

        setTimeout(() => {
          questionNumbersScrollRef.current?.scrollTo({
            x: scrollPosition,
            animated: true,
          });
        }, 100);
      }
    }
  }, [session?.currentIndex, showQuestionNumbers, scrollViewWidth, currentScrollX, isManualScrolling, session]);

  // Handle manual scrolling detection
  const handleScroll = (event: any) => {
    setCurrentScrollX(event.nativeEvent.contentOffset.x);
    setIsManualScrolling(true);

    // Clear existing timeout
    if (manualScrollTimeoutRef.current) {
      clearTimeout(manualScrollTimeoutRef.current);
    }

    // Reset manual scrolling flag after user stops scrolling
    manualScrollTimeoutRef.current = setTimeout(() => {
      setIsManualScrolling(false);
    }, 1000); // Allow 1 second of no scrolling before enabling auto-scroll again
  };

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (manualScrollTimeoutRef.current) {
        clearTimeout(manualScrollTimeoutRef.current);
      }
    };
  }, []);

  if (!session || !currentQuestion) {
    return null;
  }

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleAnswerSelect = (answerIndex: number) => {
    selectAnswer(answerIndex);
    // Auto-advance to next question for smoother UX (except on last question or while submitting)
    if (!isSubmitting && session && session.currentIndex < session.questions.length - 1) {
      setTimeout(() => {
        // Double-check not submitting and still within bounds before advancing
        const store = useExamStore.getState();
        const currentIndex = store.session?.currentIndex ?? 0;
        const total = store.session?.questions.length ?? 0;
        if (!store.isAutoSubmitted && store.isActive && currentIndex < total - 1) {
          nextQuestion();
        }
      }, 150);
    }
  };

  const handleBackPress = () => {
    setShowExitModal(true);
  };

  const handleConfirmExit = () => {
    setShowExitModal(false);
    router.dismissTo('/');
  };

  const handleCancelExit = () => {
    setShowExitModal(false);
  };

  const handleSubmitPress = () => {
    setShowSubmitModal(true);
  };

  const handleConfirmSubmit = () => {
    setShowSubmitModal(false);
    handleSubmitExam();
  };

  const handleCancelSubmit = () => {
    setShowSubmitModal(false);
  };

  const handleSubmitExam = async () => {
    try {
      setIsSubmitting(true);
      const submitStartTime = Date.now();
      submitExam();

      // Record all answers to database sequentially to avoid transaction conflicts
      for (let index = 0; index < session.questions.length; index++) {
        const question = session.questions[index];
        const userAnswer = session.answers[index]; // This is the original answer index

        if (userAnswer !== undefined) {
          // Find the correct option in the shuffled options
          const correctShuffledIndex = question.options.findIndex(opt => opt.isCorrect);
          const correctOptionLabel = ['A', 'B', 'C', 'D'][correctShuffledIndex];

          // Check if answer is correct using the original answer index
          // We need to check correctness against the original options, not the shuffled display
          let isCorrect = false;
          let selectedOptionLabel = 'A';

          if (correctShuffledIndex !== -1) {
            const optionMap = session.paperMap.optionMaps[index];
            const correctOriginalIndex = optionMap ? optionMap.displayToOriginal[correctShuffledIndex] : correctShuffledIndex;
            isCorrect = userAnswer === correctOriginalIndex;

            // Convert original answer index to displayed answer index for label
            const displayAnswerIndex = optionMap ? optionMap.originalToDisplay[userAnswer] : userAnswer;
            selectedOptionLabel = ['A', 'B', 'C', 'D'][displayAnswerIndex];
          }

          try {
            await recordAnswer({
              questionId: question.id,
              volume: question.volume,
              chapter: 1, // Default chapter
              isCorrect: isCorrect,
              mode: 'exam',
              sessionId: session.id,
              selectedOption: selectedOptionLabel as 'A' | 'B' | 'C' | 'D',
              correctOption: correctOptionLabel as 'A' | 'B' | 'C' | 'D',
              timeSpent: 0, // Individual time tracking not implemented for exam
            });
          } catch (answerError) {
            console.error(`Failed to record answer for question ${index + 1}:`, answerError);
            // Continue with other answers even if one fails
          }
        }
      }
      // Ensure at least 1s wait before navigating to result
      const elapsedMs = Date.now() - submitStartTime;
      if (elapsedMs < 1000) {
        await new Promise(resolve => setTimeout(resolve, 1000 - elapsedMs));
      }

      router.replace('/exam/result');
    } catch (error) {
      console.error('Failed to submit exam:', error);
      Alert.alert(
        '提交失敗',
        '無法提交考試結果，請稍後再試。',
        [{ text: '確定' }]
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderTimer = () => (
    <View className="px-3 py-2 rounded-2xl w-25">
      <View className="flex-row items-center justify-center gap-1.5">
        <MaterialIcons
          name="schedule"
          size={16}
          color={stats.timeRemaining <= 300 ? COLORS.ERROR : COLORS.SECONDARY_TEXT}
        />
        <Text className={`text-base font-semibold ${stats.timeRemaining <= 300 ? 'text-error' : 'text-text'}`}>
          {formatTime(stats.timeRemaining)}
        </Text>
      </View>
    </View>
  );

  // Dynamic header configuration
  useLayoutEffect(() => {
    navigation.setOptions({
      headerRight: () => renderTimer(),
      headerLeft: () => (
        <TouchableOpacity onPress={handleBackPress}>
          <Ionicons name="chevron-back" size={28} color="black" />
        </TouchableOpacity>
      ),
    });
  }, [navigation, stats.timeRemaining]);

  return (
    <SafeAreaView className="flex-1 bg-background" edges={['bottom', 'left', 'right']}>
      <StatusBar style="dark" />

      {/* Question Numbers */}
      <QuestionNumbersBar
        ref={questionNumbersScrollRef}
        totalQuestions={session.questions.length}
        currentIndex={session.currentIndex}
        questionStates={session.questions.map((_, index) =>
          session.answers[index] !== undefined ? 'answered' : 'unanswered'
        )}
        onQuestionPress={jumpToQuestion}
        onLayout={(event) => {
          setScrollViewWidth(event.nativeEvent.layout.width);
        }}
        onScroll={handleScroll}
      />

      {/* Main Content */}
      <ScrollView className="flex-1 pt-2.5 p-5" showsVerticalScrollIndicator={false}>
        <QuestionCard
          question={currentQuestion}
          questionNumber={session.currentIndex + 1}
          totalQuestions={session.questions.length}
        />

        <OptionsList
          options={currentQuestion.options}
          selectedOption={selectedAnswer ?? undefined}
          onOptionSelect={handleAnswerSelect}
          showAnswer={DEBUG.SHOW_CORRECT_ANSWER_IN_EXAM} // Show correct answer in debug mode
          disabled={isSubmitting}
          disableShuffling={true} // Disable shuffling in exam mode - options are already shuffled in question generation
        />
      </ScrollView>

      {/* Sticky Bottom Navigation */}
      <View className={`bg-background border-t border-border p-4 ${Platform.OS === 'ios' ? '' : 'mb-5'}`}>
        <View className="flex-row justify-between mb-0 gap-3">
          <Button
            title="上一題"
            onPress={previousQuestion}
            disabled={session.currentIndex <= 0 || isSubmitting}
            style={{ flex: 1, backgroundColor: COLORS.THEME }}
            size='medium'
          />

          <Button
            title="下一題"
            onPress={nextQuestion}
            disabled={session.currentIndex >= session.questions.length - 1 || isSubmitting}
            style={{ flex: 1, backgroundColor: COLORS.THEME }}
            size='medium'
          />

          {/* Show submit button when all questions are answered OR when testing flag is enabled */}
        {(stats.answeredCount === session.questions.length || DEBUG.SHOW_SUBMIT_BUTTON_FOR_TESTING) && (
          <Button
            title="提交考試"
            onPress={handleSubmitPress}
            loading={isSubmitting}
            disabled={isSubmitting}
            style={{ flex: 1, backgroundColor: COLORS.THEME }}
            size='medium'
          />
        )}
        </View>
      </View>
      {isSubmitting && (
        <View className="absolute top-0 left-0 right-0 bottom-0 bg-black/20 items-center justify-center">
          <Text className="px-4 py-2.5 rounded-xl bg-card text-text text-base font-semibold">正在提交，請稍候…</Text>
        </View>
      )}

      {/* Exit Confirmation Modal */}
      <ConfirmationModal
        visible={showExitModal}
        title="您確定要離開考試嗎？"
        content={['離開後將所有答題記錄將會遺失。']}
        onCancel={handleCancelExit}
        onConfirm={handleConfirmExit}
        cancelText="繼續考試"
        confirmText="確認離開"
      />

      {/* Submit Confirmation Modal */}
      <ConfirmationModal
        visible={showSubmitModal}
        title="確認提交考試？"
        content={[
          `您已完成 ${stats.answeredCount}/${session.questions.length} 題`
        ]}
        onCancel={handleCancelSubmit}
        onConfirm={handleConfirmSubmit}
        cancelText="繼續考試"
        confirmText="確認提交"
      />
    </SafeAreaView>
  );
}

