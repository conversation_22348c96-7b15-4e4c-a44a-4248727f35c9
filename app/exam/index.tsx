import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useState } from 'react';
import { Al<PERSON>, ScrollView, Text, View } from 'react-native';

import { <PERSON><PERSON>, Card } from '../../src/components/common';
import { QuestionManager } from '../../src/services/questions/manager';
import { useExamStore } from '../../src/store/useExamStore';
import { COLORS, EXAM_RULES } from '../../src/utils/constants';

export default function ExamEntryScreen() {
  const [isLoading, setIsLoading] = useState(false);
  const { startExam } = useExamStore();

  // Define notes array for dynamic rendering
  const examNotes = [
    '考試過程中不會顯示正確答案',
    '可以隨時切換題目',
    '已作答的題目會保存您的選擇',
    '提交按鈕會在作答所有問題後出現',
    '時間到達後系統會自動提交',
    '未作答的題目將被視為錯誤',   
  ];

  // Define exam rules array for dynamic rendering
  const examRules = [
    {
      title: '題目分配',
      description: `目前總共 ${EXAM_RULES.TOTAL_QUESTIONS} 題（第1-5冊）`,
      content: [
        `第1冊：${EXAM_RULES.QUESTIONS_PER_VOLUME[1]} 題`,
        `第2冊：${EXAM_RULES.QUESTIONS_PER_VOLUME[2]} 題`,
        `第3冊：${EXAM_RULES.QUESTIONS_PER_VOLUME[3]} 題`,
        `第4冊：${EXAM_RULES.QUESTIONS_PER_VOLUME[4]} 題`,
        `第5冊：${EXAM_RULES.QUESTIONS_PER_VOLUME[5]} 題`
      ]
    },
    {
      title: '考試時間',
      content: [`限時 ${EXAM_RULES.TIME_LIMIT_MINUTES} 分鐘`]
    },
    {
      title: '合格條件',
      content: [`錯誤題數不超過 ${EXAM_RULES.MAX_WRONG_TOTAL} 題`,
        `且每冊錯誤題數不超過 ${EXAM_RULES.MAX_WRONG_PER_VOLUME} 題`]
    }
  ];

  const handleStartExam = async () => {
    setIsLoading(true);
    try {
      // Generate exam questions with paper map
      const examData = await QuestionManager.generateExamQuestions();

      // Validate question set
      if (!QuestionManager.validateQuestionSet(examData.questions)) {
        throw new Error('Invalid question set generated');
      }

      // Start the exam
      startExam(examData);
      router.replace('/exam/session');
    } catch (error) {
      console.error('Failed to start exam:', error);
      Alert.alert(
        '錯誤',
        '無法載入考試題目，請檢查您的網路連接或稍後再試。',
        [{ text: '確定' }]
      );
    } finally {
      setIsLoading(false);
    }
  };


  return (
    <View className="flex-1 bg-background">
      <StatusBar style="dark" />

      <ScrollView
        className="flex-1"
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ padding: 20, paddingTop: 0, paddingBottom: 40 }}
      >
        {/* Header */}
        <View className="items-center mb-4">
          <Text className="text-3xl font-bold text-text text-center mb-2">澳門駕駛理論考試</Text>
        </View>

        {/* Exam Rules Card */}
        <Card className="mb-5 shadow-sm">
          <Text className="text-20 font-bold text-text mb-2.5 text-center">考試規則</Text>

          {examRules.map((rule, index) => (
            <View key={index} className={`mb-3 pl-4 ${index !== examRules.length - 1 ? 'border-b border-border pb-3' : 'mb-0 pb-0'}`}>
              <Text className="text-base font-semibold text-theme-text mb-2">{rule.title}</Text>
              {/* Show description for first rule (題目分配) */}
              {index === 0 && rule.description && (
                <Text className="text-14 text-text-secondary mb-3 text-left">{rule.description}</Text>
              )}

              {/* Special layout for first rule item (題目分配) */}
              {index === 0 ? (
                <View className="flex-row justify-between mb-1">
                  <View className="flex-1 mr-2.5">
                    {rule.content.slice(0, Math.ceil(rule.content.length / 2)).map((item, subIndex) => (
                      <View key={subIndex} className="flex-row items-start mb-0">
                        <Text className="text-base text-text mr-2.5">•</Text>
                        <Text className="text-14 text-text leading-5 mb-1">
                          {item}
                        </Text>
                      </View>
                    ))}
                  </View>
                  <View className="flex-1">
                    {rule.content.slice(Math.ceil(rule.content.length / 2)).map((item, subIndex) => (
                      <View key={subIndex + Math.ceil(rule.content.length / 2)} className="flex-row items-start mb-0">
                        <Text className="text-base text-text mr-2.5">•</Text>
                        <Text className="text-14 text-text leading-5 mb-1">
                          {item}
                        </Text>
                      </View>
                    ))}
                  </View>
                </View>
              ) : (
                <View className="mb-1">
                  {rule.content.map((item, subIndex) => (
                    <View key={subIndex} className="flex-row items-start mb-0">
                      <Text className="text-base text-text mr-2.5">•</Text>
                      <Text className="text-14 text-text leading-5 mb-1">
                        {item}
                      </Text>
                    </View>
                  ))}
                </View>
              )}
            </View>
          ))}
        </Card>

        {/* Important Notes Card */}
        <Card className="mb-5 bg-yellow-50 shadow-sm">
          <Text className="text-20 font-bold text-text mb-2.5 text-center">重要提醒</Text>

          <View className="pl-2.5">
            {examNotes.map((note, index) => (
              <View key={index} className="flex-row items-start mb-1.5">
                <Text className="text-base text-warning mr-2.5">•</Text>
                <Text className="flex-1 text-14 text-text leading-5">
                  {note}
                </Text>
              </View>
            ))}
          </View>
        </Card>

        {/* Action Buttons */}
        <View className="gap-4">
          <Button
            title="開始考試"
            size="large"
            onPress={handleStartExam}
            loading={isLoading}
            style={{ backgroundColor: COLORS.THEME, paddingVertical: 16 }}
          />
        </View>
      </ScrollView>
    </View>
  );
}