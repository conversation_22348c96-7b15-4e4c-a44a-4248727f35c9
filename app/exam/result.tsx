import { router, useNavigation } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useLayoutEffect, useRef, useState } from 'react';
import { Platform, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { COLORS } from '@/src/utils/constants';
import { Ionicons } from '@expo/vector-icons';
import { Button, Card } from '../../src/components/common';
import { OptionsList, QuestionCard, QuestionNumbersBar } from '../../src/components/question';
import { useExamStore } from '../../src/store/useExamStore';
import { ProcessedQuestion } from '../../src/types/question';

export default function ExamResultScreen() {
  const navigation = useNavigation();
  const { session, examResult } = useExamStore();

  // All hooks must be called before any early returns
  const [reviewMode, setReviewMode] = useState(false);
  const [currentReviewIndex, setCurrentReviewIndex] = useState(0);
  const [scrollViewWidth, setScrollViewWidth] = useState(0);
  const [currentScrollX, setCurrentScrollX] = useState(0);
  const [isManualScrolling, setIsManualScrolling] = useState(false);
  const [showQuestionNumbers, setShowQuestionNumbers] = useState(true);
  const questionNumbersScrollRef = useRef<ScrollView>(null);
  const manualScrollTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  // Auto-scroll to current question in question numbers bar during review (intelligent scrolling)
  useEffect(() => {
    if (reviewMode && showQuestionNumbers && questionNumbersScrollRef.current && scrollViewWidth > 0 && !isManualScrolling) {
      const questionWidth = 36; // Width of each question number button
      const questionGap = 8; // Gap between buttons
      const itemWidth = questionWidth + questionGap;
      const currentQuestionPosition = currentReviewIndex * itemWidth;

      // Calculate visible area with buffer
      const buffer = itemWidth * 1.5; // Show 1.5 questions buffer on each side
      const visibleStart = currentScrollX;
      const visibleEnd = currentScrollX + scrollViewWidth;

      // Check if current question is outside visible area (with buffer)
      const questionStart = currentQuestionPosition;
      const questionEnd = currentQuestionPosition + questionWidth;

      const needsScrolling = questionStart < visibleStart + buffer || questionEnd > visibleEnd - buffer;

      if (needsScrolling) {
        // Center the current question in the viewport
        const scrollPosition = Math.max(0, currentQuestionPosition - (scrollViewWidth / 2) + (questionWidth / 2));

        setTimeout(() => {
          questionNumbersScrollRef.current?.scrollTo({
            x: scrollPosition,
            animated: true,
          });
        }, 100);
      }
    }
  }, [currentReviewIndex, reviewMode, showQuestionNumbers, scrollViewWidth, currentScrollX, isManualScrolling]);

  // Dynamic header configuration
  useLayoutEffect(() => {
    if (reviewMode) {
      // Answer details header
      navigation.setOptions({
        title: '答題詳情',
        headerLeft: () => (
          <TouchableOpacity onPress={() => setReviewMode(false)} className="">
            <Ionicons name="chevron-back" size={28} color="black" />
          </TouchableOpacity>
        ),
        headerRight: () => null,
      });
    } else {
      // Exam result header
      navigation.setOptions({
        title: '考試結果',
        headerLeft: () => null,
        headerBackVisible: false,
        headerRight: () => null,
      });
    }
  }, [navigation, reviewMode]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (manualScrollTimeoutRef.current) {
        clearTimeout(manualScrollTimeoutRef.current);
      }
    };
  }, []);

  // Early return AFTER all hooks
  if (!examResult || !session) {
    return null;
  }

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
      return `${hours}時${minutes}分${remainingSeconds}秒`;
    }
    return `${minutes}分${remainingSeconds}秒`;
  };

  const getFailureReasonText = () => {
    if (examResult.failureReason === 'too_many_wrong_total') {
      return `總錯誤題數超過限制（${examResult.wrongCount} > 8）`;
    }
    if (examResult.failureReason === 'too_many_wrong_per_set') {
      const failedSets = [];
      if (examResult.setResults.set3.wrongCount > 2) failedSets.push('第3冊');
      if (examResult.setResults.set4.wrongCount > 2) failedSets.push('第4冊');
      if (examResult.setResults.set5.wrongCount > 2) failedSets.push('第5冊');
      return `${failedSets.join('、')}錯誤題數超過限制（>2題）`;
    }
    return '';
  };

  const getAllQuestionsForReview = (): {
    question: ProcessedQuestion;
    userAnswer: number;
    isCorrect: boolean;
    setName: string;
  }[] => {
    if (!session) {
      return [];
    }

    const allQuestions: {
      question: ProcessedQuestion;
      userAnswer: number;
      isCorrect: boolean;
      setName: string;
    }[] = [];

    // Use the session questions to maintain the exact exam order
    session.questions.forEach((question, examIndex) => {
      // Get the user's answer for this question (stored as original option index)
      const originalUserAnswer = session.answers[examIndex];

      // Keep the shuffled options order as shown during the exam
      // The question.options are already in the shuffled order from the exam session
      const displayQuestion = question;

      // Convert original answer index to displayed answer index for result display
      let displayUserAnswer = -1;
      if (originalUserAnswer !== undefined) {
        const optionMap = session.paperMap.optionMaps[examIndex];
        displayUserAnswer = optionMap ? optionMap.originalToDisplay[originalUserAnswer] : originalUserAnswer;
      }

      // Get set information from paper map
      const questionMap = session.paperMap.questionMap[examIndex];
      const setNumber = questionMap ? questionMap.setNumber : 1;
      const setNames = ['第1冊', '第2冊', '第3冊', '第4冊', '第5冊'];
      const setName = setNames[setNumber - 1];

      // Check if answer is correct using the original answer index
      // We need to check correctness against the original options, not the shuffled display
      let isCorrect = false;
      if (originalUserAnswer !== undefined) {
        // Find the correct option in the shuffled options
        const correctShuffledIndex = displayQuestion.options.findIndex(opt => opt.isCorrect);
        if (correctShuffledIndex !== -1) {
          const optionMap = session.paperMap.optionMaps[examIndex];
          const correctOriginalIndex = optionMap ? optionMap.displayToOriginal[correctShuffledIndex] : correctShuffledIndex;
          isCorrect = originalUserAnswer === correctOriginalIndex;
        }
      }

      allQuestions.push({
        question: displayQuestion,
        userAnswer: displayUserAnswer,
        isCorrect,
        setName,
      });
    });

    return allQuestions;
  };

  const handleGoHome = () => {
    // Just navigate cleanly - next exam will reset state anyway
    router.dismissTo('/');
  };

  const handleStartReview = () => {
    setReviewMode(true);
    setCurrentReviewIndex(0);
  };

  const renderResult = () => (
    <View className="p-5 pb-10">
      {/* Result Header */}
      <View className="items-center py-2.5 px-5 mb-2.5">
        <Text className="text-60 mb-4">
          {examResult.isPassed ? '🎉' : '😞'}
        </Text>
        <Text className={`text-28 font-bold mb-2 text-center ${examResult.isPassed ? 'text-success' : 'text-error'}`}>
          {examResult.isPassed ? '恭喜合格！' : '很遺憾，未能合格'}
        </Text>
        <Text className="text-base text-text-light text-center">
          {examResult.isPassed
            ? '您已通過澳門考車理論考試'
            : '請繼續努力，下次一定可以'}
        </Text>
      </View>

      {/* Score Summary */}
      <Card className="mb-5">
        <Text className="text-20 font-bold text-text mb-5 text-center">考試成績</Text>

        <View className="flex-row justify-between items-center mb-4 pb-4 border-b border-border">
          <Text className="text-base text-text">總分</Text>
          <Text className="text-18 font-semibold text-theme-text">
            {examResult.score}/{examResult.totalQuestions}
          </Text>
        </View>

        <View className="flex-row justify-between items-center mb-4 pb-4 border-b border-border">
          <Text className="text-base text-text">正確率</Text>
          <Text className="text-18 font-semibold text-theme-text">
            {Math.round((examResult.score / examResult.totalQuestions) * 100)}%
          </Text>
        </View>

        <View className="flex-row justify-between items-center mb-4 pb-4 border-b border-border">
          <Text className="text-base text-text">用時</Text>
          <Text className="text-18 font-semibold text-theme-text">
            {formatTime(examResult.timeSpent)}
          </Text>
        </View>

        {!examResult.isPassed && (
          <View className="p-4 rounded-lg mt-2.5">
            <Text className="text-base text-error text-center">
              {getFailureReasonText()}
            </Text>
          </View>
        )}
      </Card>

      {/* Set Results */}
      <Card className="mb-8">
        <Text className="text-20 font-bold text-text mb-5 text-center">各冊成績</Text>

        {Object.entries(examResult.setResults).map(([setKey, setResult]) => (
          <View key={setKey} className="mb-5 pb-5 border-b border-border">
            <View className="flex-row justify-between items-center mb-2">
              <Text className="text-base font-semibold text-text">{setResult.name}</Text>
              <Text className={`text-base font-semibold ${setResult.wrongCount > 2 ? 'text-error' : 'text-success'}`}>
                {setResult.correctCount}/{setResult.totalQuestions}
              </Text>
            </View>

            <View className="h-2 bg-border rounded mb-2">
              <View
                className="h-full bg-success rounded"
                style={{ width: `${(setResult.correctCount / setResult.totalQuestions) * 100}%` }}
              />
            </View>

            <Text className="text-14 text-text-light">
              正確: {setResult.correctCount} 題 | 錯誤: {setResult.wrongCount} 題
            </Text>
          </View>
        ))}
      </Card>

      {/* Action Buttons */}
      <View className="gap-4">
        <Button
          title="查看答題詳情"
          onPress={handleStartReview}
        />

        <Button
          title="返回首頁"
          onPress={handleGoHome}
          variant="secondary"
        />
      </View>
    </View>
  );

  const renderReview = () => {
    const questions = getAllQuestionsForReview();
    const currentItem = questions[currentReviewIndex];

    if (!currentItem) return null;

    return (
      <>
        {/* Question Numbers */}
        {showQuestionNumbers && (
          <QuestionNumbersBar
            ref={questionNumbersScrollRef}
            totalQuestions={questions.length}
            currentIndex={currentReviewIndex}
            questionStates={questions.map(q => q.isCorrect ? 'correct' : 'wrong')}
            onQuestionPress={setCurrentReviewIndex}
            onLayout={(event) => {
              setScrollViewWidth(event.nativeEvent.layout.width);
            }}
            onScroll={handleScroll}
          />
        )}

        <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
          <View className="p-5 pt-2.5 pb-10">
            {/* Question Content */}
            <QuestionCard
              question={currentItem.question}
              questionNumber={currentReviewIndex + 1}
              totalQuestions={questions.length}
            />

            <OptionsList
              options={currentItem.question.options}
              selectedOption={currentItem.userAnswer === -1 ? undefined : currentItem.userAnswer}
              onOptionSelect={() => { }} // Read-only in review mode
              showAnswer={true}
              disabled={true}
              disableShuffling={true} // Disable shuffling in exam result - show same order as during exam
            />
          </View>
        </ScrollView>

        {/* Sticky Bottom Navigation */}
        <View className={`bg-background border-t border-border p-4 ${Platform.OS === 'ios' ? '' : 'mb-5'}`}>
          <View className="flex-row justify-between mb-0 gap-3">
            <Button
              title="上一題"
              onPress={() => setCurrentReviewIndex(Math.max(0, currentReviewIndex - 1))}
              disabled={currentReviewIndex <= 0}
              size="medium"
              style={{ flex: 1, backgroundColor: COLORS.THEME }}
            />

            <Button
              title="下一題"
              onPress={() => setCurrentReviewIndex(Math.min(questions.length - 1, currentReviewIndex + 1))}
              disabled={currentReviewIndex >= questions.length - 1}
              size="medium"
              style={{ flex: 1, backgroundColor: COLORS.THEME }}
            />
          </View>
        </View>
      </>
    );
  };

  // Handle manual scrolling detection
  const handleScroll = (event: any) => {
    setCurrentScrollX(event.nativeEvent.contentOffset.x);
    setIsManualScrolling(true);

    // Clear existing timeout
    if (manualScrollTimeoutRef.current) {
      clearTimeout(manualScrollTimeoutRef.current);
    }

    // Reset manual scrolling flag after user stops scrolling
    manualScrollTimeoutRef.current = setTimeout(() => {
      setIsManualScrolling(false);
    }, 1000); // Allow 1 second of no scrolling before enabling auto-scroll again
  };

  return (
    <SafeAreaView className="flex-1 bg-background" edges={['bottom', 'left', 'right']}>
      <StatusBar style="dark" />

      {reviewMode ? (
        renderReview()
      ) : (
        <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
          {renderResult()}
        </ScrollView>
      )}
    </SafeAreaView>
  );
}

