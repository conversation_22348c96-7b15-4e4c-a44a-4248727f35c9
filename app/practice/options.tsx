import { router } from 'expo-router';
import React, { useState } from 'react';
import { Al<PERSON>, ScrollView, Text, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { <PERSON><PERSON>, Card, LoadingSpinner } from '../../src/components/common';
import { useStatistics } from '../../src/hooks/useStatistics';
import { QuestionManager } from '../../src/services/questions/manager';
import { usePracticeStore } from '../../src/store/usePracticeStore';
import { PracticeConfig } from '../../src/types/session';
import { COLORS, VOLUMES } from '../../src/utils/constants';

export default function PracticeOptionsScreen() {
  const [loading, setLoading] = useState(false);
  const [selectedVolumes, setSelectedVolumes] = useState<number[]>([]);
  const [includeUnseen] = useState(true);
  const [practiceMode] = useState<'sequential' | 'random'>('random');
  const { startSession } = usePracticeStore();
  const { volumeDisplayStats } = useStatistics();
  const actionDisabled = selectedVolumes.length === 0;

  const startPractice = async (config: PracticeConfig) => {
    setLoading(true);
    
    try {
      const questions = await QuestionManager.generatePracticeQuestions(config);
      
      if (questions.length === 0) {
        Alert.alert('沒有題目', '所選範圍內沒有找到題目，請選擇其他選項。');
        return;
      }

      // Check if all questions in selected volumes have been mastered
      let allMastered = false;
      if (config.includeUnseen && volumeDisplayStats) {
        const selectedVolumeStats = volumeDisplayStats.filter(stats => 
          config.volumes.includes(stats.volume)
        );
        allMastered = selectedVolumeStats.every(stats => 
          stats.correct >= stats.total
        );
      }

      if (allMastered) {
        Alert.alert(
          '恭喜完成！', 
          '您已掌握所選冊別的所有題目！現在將開始複習模式，您可以重新練習來保持熟練度。',
          [{ text: '開始複習', onPress: () => {
            startSession(config, questions);
            router.replace('/practice/session');
          }}]
        );
      } else {
        startSession(config, questions);
        router.replace('/practice/session');
      }
    } catch (error) {
      console.error('Failed to start practice:', error);
      Alert.alert('錯誤', '無法載入練習題目，請稍後再試。');
    } finally {
      setLoading(false);
    }
  };

  const toggleVolumeSelection = (volume: number) => {
    // Only allow volume 3 to be selected
    if (volume !== 3) {
      Alert.alert('即將推出', '此冊別練習功能即將推出，敬請期待！');
      return;
    }
    
    setSelectedVolumes(prev => {
      if (prev.includes(volume)) {
        return prev.filter(v => v !== volume);
      } else {
        return [...prev, volume].sort();
      }
    });
  };


  const handleStartPractice = () => {
    if (selectedVolumes.length === 0) {
      Alert.alert('請選擇冊別', '請至少選擇一個冊別進行練習。');
      return;
    }

    const config: PracticeConfig = {
      volumes: selectedVolumes,
      chapter: null,
      mode: practiceMode,
      includeWrongQuestions: false,
      includeBookmarked: false,
      includeUnseen,
    };

    startPractice(config);
  };

  if (loading) {
    return (
      <SafeAreaView className="flex-1 bg-background">
        <LoadingSpinner message="正在載入題目..." />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-background" edges={['bottom', 'left', 'right']}>
      {/* <StatusBar style="auto" /> */}
      <ScrollView
        className="flex-1"
      >
        <View className="mb-6 px-5">
          <View className="mb-4 pt-4">
            <Text className="text-22 font-bold mb-1.5 text-text tracking-tight">選擇練習冊別</Text>
            <Text className="text-15 text-text-light leading-5">可選擇一個或多個冊別進行練習</Text>
          </View>

          <View className="flex-row flex-wrap justify-between">
            {Array.from({ length: VOLUMES.TOTAL }, (_, i) => i + 1).map(volume => {
              const isSelected = selectedVolumes.includes(volume);
              const isDisabled = volume !== 3;
              const volumeStats = volumeDisplayStats?.find(v => v.volume === volume);
              const accuracy = volumeStats ? 
                (volumeStats.correct / Math.max(1, volumeStats.correct + volumeStats.wrong) * 100) : 0;
              
              return (
                <Card
                  key={volume}
                  className={`w-[48%] mb-4 border-2 ${isSelected ? 'border-accent' : 'border-transparent'} rounded-2xl bg-card shadow-sm p-4 ${isDisabled ? 'opacity-80 bg-card-disabled' : ''}`}
                  onPress={() => toggleVolumeSelection(volume)}
                >
                  <View className="flex-row justify-between items-center mb-1">
                    <Text className={`text-base font-semibold text-text ${isSelected ? 'font-bold' : ''} ${isDisabled ? 'opacity-70 text-text-secondary' : ''}`}>
                      第 {volume} 冊
                    </Text>
                    <View className={`w-5.5 h-5.5 rounded-full border-2 items-center justify-center ${isSelected ? 'bg-accent border-accent' : 'border-border bg-card'} ${isDisabled ? 'border-text-secondary bg-border' : ''}`}>
                      {isSelected && <Text className="text-white text-12 font-bold">✓</Text>}
                    </View>
                  </View>
                  <Text className={`text-14 text-text mb-2 ${isDisabled ? 'opacity-70 text-text-secondary' : ''}`}>
                    {volumeStats?.title || VOLUMES.NAMES[volume as keyof typeof VOLUMES.NAMES]}
                  </Text>

                  {isDisabled ? (
                    <Text className="text-12 text-warning font-semibold">
                      即將推出
                    </Text>
                  ) : volumeStats && (volumeStats.correct + volumeStats.wrong > 0) ? (
                    <View className="mt-1">
                      <Text
                        className="text-12 font-semibold mb-0.5"
                        style={{ color: accuracy >= 80 ? COLORS.SUCCESS : accuracy >= 60 ? COLORS.WARNING : COLORS.ERROR }}
                      >
                        正確率: {accuracy.toFixed(0)}%
                      </Text>
                      <Text className="text-12 text-text-secondary">
                        已練習 {volumeStats.correct + volumeStats.wrong} / {volumeStats.total} 題
                      </Text>
                    </View>
                  ) : (
                    <Text className="text-12 text-text-secondary">
                      共 {volumeStats?.total || VOLUMES.COUNTS[volume as keyof typeof VOLUMES.COUNTS]} 道題目 · 未開始練習
                    </Text>
                  )}
                </Card>
              );
            })}
          </View>
        </View>
      </ScrollView>

      {/* Sticky Start Practice Button */}
      <View className="px-5 py-4 bg-background border-t border-border">
        <Button
          title={selectedVolumes.length > 0 ? `開始練習 (已選 ${selectedVolumes.length} 冊)` : '請選擇冊別'}
          onPress={handleStartPractice}
          disabled={actionDisabled}
        />
      </View>
    </SafeAreaView>
  );
}

