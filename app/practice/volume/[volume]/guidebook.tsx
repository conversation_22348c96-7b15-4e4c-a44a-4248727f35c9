import { Ionicons } from '@expo/vector-icons';
import { useLocalSearchParams, useNavigation } from 'expo-router';
import React, { useEffect, useLayoutEffect, useState } from 'react';
import { Alert, Image, Modal, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { Card, LoadingSpinner } from '../../../../src/components/common';
import { getDatabase } from '../../../../src/services/database/init';
import { IMAGE_MAP, ImageKey } from '../../../../src/services/questions/imageMap';
import { QuestionLoader } from '../../../../src/services/questions/loader';
import { ProcessedQuestion } from '../../../../src/types/question';
import { COLORS, VOLUMES } from '../../../../src/utils/constants';

// Volume tips data
const VOLUME_TIPS = {
  2: `單行線
車係左轉左，係右轉右

雙行線
單係左轉左，係中線轉右

然後順序係
永遠有倒三角形最後行
車係左轉左先行
再到車左邊冇車先行
然後到部車條線唔阻人先行`,

  3: `一年至三年
兩個月至六個月

燈600
橋900
無牌5000-25,000
累犯 乘2`,

  4: `3000跟四位數字就3000
有50，600，一定係600
1000最大就揀600
200，400，600，900，就900
遠光燈1500

組合
前面乘5等於後面
有累犯就1200（優先）
必選600-2,500（優先）
必選2000-10,000（優先）
必選4000-20,000（優先）

撞車後不理會：3年徒刑
無牌駕駛：6個月＆一萬-五萬
慣常酗酒：1-3年
利用其他方法解決：1年＆120日

300
超載
起步
單車
行人
文件
響安
突然減速

600
裝貨
不靠左停
頭盔
26通常600，但手推是300
影響環境(排煙，噪音)
未被超越就加速
不方便他人超車

900
運載
阻塞不讓對頭車
讓出左方在左方超車

善用排除法
100,200,400,500,700,800,1000,
1200,2000,4000,5000,6000
只有300,600,900
1500是遠光燈
3000四位數內必選

300：超載、開門、重新起步(集車)、死火(冇打燈)、行入特定車輛道路、單車載人、行人路推車、行人路、交通燈、執照車牌文件、響安、突然減速

600：集體客運車、卸貨、死火、整車、超載(電單車)、P牌、頭盔、電單車離手/落腳、電單車拖野、電單車並排、車走行人路、電單車運野、排氣漏油嘈音、用電話、重新起動、有距離、示意減速、示寬燈、未超就加速、減速方便他人超車

900：運載、倒車、轉彎、靠左行、讓左方行先、讓對頭車先行、超車

1500：遠光燈

3000：運超過20%、運危險物品不打燈、車內安裝干擾儀器

撞車後不理會：3年徒刑
無牌駕駛：6個月&一萬-五萬
慣常酗酒：1-3年

組合：
600 - 2500
1200 - 5000 (累犯)
(除以5)
1000 - 5000 (引橋不讓)
2000 - 10000
2500 - 12500
4000 - 20000
5000 - 25000
6000 - 30000
12000 - 60000`,

  5: `第五冊暫無特別提示`
} as const;

// Convert Arabic numerals to Chinese numerals for small numbers (1-20)
function toChineseNumber(num: number): string {
  const digits = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
  if (num <= 10) {
    const map: Record<number, string> = { 10: '十' };
    return map[num] || digits[num];
  }
  if (num < 20) {
    return `十${digits[num - 10]}`;
  }
  if (num % 10 === 0) {
    return `${digits[Math.floor(num / 10)]}十`;
  }
  return `${digits[Math.floor(num / 10)]}十${digits[num % 10]}`;
}

export default function GuidebookScreen() {
  const { volume } = useLocalSearchParams<{ volume: string }>();
  const volumeNumber = parseInt(volume || '1', 10);
  const [questions, setQuestions] = useState<ProcessedQuestion[]>([]);
  const [loadingQuestions, setLoadingQuestions] = useState(true);
  const [expandedQuestions, setExpandedQuestions] = useState<Set<number>>(new Set());
  const [questionStats, setQuestionStats] = useState<Record<string, { isCorrect: boolean; hasAnswered: boolean }>>({});
  const [showTipsModal, setShowTipsModal] = useState(false);
  const navigation = useNavigation();

  const hasImages = volumeNumber === 1 || volumeNumber === 2;
  const chineseVolumeNumber = toChineseNumber(volumeNumber);
  const volumeNameForTitle = VOLUMES.NAMES[volumeNumber as keyof typeof VOLUMES.NAMES] || '未知冊別';

  // Dynamic header configuration
  useLayoutEffect(() => {
    navigation.setOptions({
      title: `學習手冊 - 第${chineseVolumeNumber}冊`,
    });
  }, [navigation, chineseVolumeNumber]);

  // Dynamic header configuration
  useLayoutEffect(() => {
    navigation.setOptions({
      headerRight: () => {
        return (
         <TouchableOpacity className="w-12 h-12 bg-warning/10 rounded-full items-center justify-center mr-3" onPress={() => setShowTipsModal(true)}>
          <Ionicons name="bulb-outline" size={28} color={COLORS.WARNING} />
        </TouchableOpacity>
        )
      },
    });
  }, [navigation]);

  useEffect(() => {
    loadVolumeQuestions();
  }, [volumeNumber]);

  // Load question statistics
  useEffect(() => {
    if (questions.length === 0) return;

    const loadQuestionStats = async () => {
      try {
        const db = getDatabase();
        const stats: Record<string, { isCorrect: boolean; hasAnswered: boolean }> = {};

        for (const question of questions) {
          // Get the most recent answer record for this question AND volume
          const result = await db.getFirstAsync(
            `SELECT is_correct
             FROM answer_records
             WHERE question_id = ? AND volume = ? AND mode = 'practice'
             ORDER BY created_at DESC
             LIMIT 1`,
            [question.id, volumeNumber]
          ) as { is_correct: number | null } | null;

          if (result !== null) {
            // Question has been answered
            stats[question.id.toString()] = {
              isCorrect: result.is_correct === 1,
              hasAnswered: true
            };
          } else {
            // Question has not been answered
            stats[question.id.toString()] = {
              isCorrect: false,
              hasAnswered: false
            };
          }
        }

        setQuestionStats(stats);
      } catch (error) {
        console.error('Failed to load question stats:', error);
      }
    };

    loadQuestionStats();
  }, [questions, volumeNumber]);

  const loadVolumeQuestions = async () => {
    try {
      setLoadingQuestions(true);
      const volumeQuestions = await QuestionLoader.loadVolume(volumeNumber);
      setQuestions(volumeQuestions);
    } catch (error) {
      console.error('Failed to load volume questions:', error);
      Alert.alert('錯誤', '無法載入題目，請稍後再試。');
    } finally {
      setLoadingQuestions(false);
    }
  };

  // Get status color for a question
  const getStatusColor = (questionId: number) => {
    const stat = questionStats[questionId.toString()];
    if (!stat?.hasAnswered) return 'bg-gray-300'; // Not answered
    return stat.isCorrect ? 'bg-success' : 'bg-error'; // Correct or wrong
  };

  const toggleQuestion = (questionIndex: number) => {
    setExpandedQuestions(prev => {
      const newSet = new Set(prev);
      if (newSet.has(questionIndex)) {
        newSet.delete(questionIndex);
      } else {
        newSet.add(questionIndex);
      }
      return newSet;
    });
  };

  if (loadingQuestions) {
    return (
      <SafeAreaView className="flex-1 bg-background items-center justify-center">
        <LoadingSpinner color={COLORS.THEME} message="正在載入學習手冊..." />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-background" edges={['bottom', 'left', 'right']}>
      <ScrollView className="flex-1 p-4" showsVerticalScrollIndicator={true}>
        {/* Guidebook Card */}
        <Card className="shadow-sm">
          <View className="p-2">
            <View className="flex-row items-center mb-2">
                <View className="w-1 h-6 bg-theme rounded-full mr-3" />
                <Text className="text-16 font-semibold text-text">學習手冊</Text>
            </View>
            <Text className="text-14 text-text-light mb-4">
              第{chineseVolumeNumber}冊 - {volumeNameForTitle} ({questions.length}題)
            </Text>
            {questions.map((question, index) => {
              const isExpanded = expandedQuestions.has(index);
              const correctOption = question.options.find(opt => opt.isCorrect);

              return (
                <View key={question.id} className="mb-3">
                  <TouchableOpacity
                    onPress={() => toggleQuestion(index)}
                    className="bg-card-light rounded-lg p-4 border border-border"
                  >
                    {hasImages && question.image ? (
                      // Layout for volumes with images (1 & 2): Image left, content right
                      <View className="flex-row">
                        {/* Left side: Image */}
                        <View className="w-32 mr-4 bg-gray-100 rounded-lg overflow-hidden" style={{ height: 100 }}>
                          <Image
                            source={(IMAGE_MAP as Record<string, number>)[question.image as ImageKey] ?? { uri: question.image }}
                            style={{ height: 100, width: '100%' }}
                            resizeMode="contain"
                          />
                        </View>

                        {/* Right side: Question content */}
                        <View className="flex-1">
                          {/* Header with status and expand icon */}
                          <View className="flex-row justify-between items-start mb-1">
                            <View className="flex-row items-start flex-1 mr-2">
                              <View className={`w-3 h-3 rounded-full mr-2 mt-0.5 ${getStatusColor(question.id)}`} />
                              <Text className="text-14 text-text flex-1" numberOfLines={isExpanded ? undefined : 2}>
                                {index + 1}. {question.question}
                              </Text>
                            </View>
                            <View className="">
                              <Ionicons
                                name={isExpanded ? "chevron-up" : "chevron-down"}
                                size={20}
                                color={COLORS.TEXT_LIGHT}
                              />
                            </View>
                          </View>

                          {/* Answer preview when collapsed */}
                          {!isExpanded && correctOption && (
                            <Text className="text-12 text-success font-semibold mt-1">
                              答案：{correctOption.text}
                            </Text>
                          )}
                        </View>
                      </View>
                    ) : (
                      // Layout for volumes without images (3, 4, 5): Standard layout
                      <>
                        {/* Header with status and expand icon */}
                        <View className="flex-row justify-between items-start mb-0">
                          <View className="flex-row items-start flex-1 mr-3">
                            <View className={`w-3 h-3 rounded-full mr-2 mt-0.5 ${getStatusColor(question.id)}`} />
                            <Text className="text-14 text-text flex-1" numberOfLines={isExpanded ? undefined : 2}>
                              {index + 1}. {question.question}
                            </Text>
                          </View>
                          <View className="">
                            <Ionicons
                              name={isExpanded ? "chevron-up" : "chevron-down"}
                              size={20}
                              color={COLORS.TEXT_LIGHT}
                            />
                          </View>
                        </View>

                        {/* Answer preview when collapsed */}
                        {!isExpanded && correctOption && (
                          <Text className="text-12 text-success font-semibold mt-2">
                            答案：{correctOption.text}
                          </Text>
                        )}
                      </>
                    )}

                    {/* Expanded Options */}
                    {isExpanded && (
                      <View className="mt-4 pt-4 border-t border-border">
                        {question.options.map((option, optionIndex) => (
                          <View key={optionIndex} className="flex-row items-start mb-2">
                            <View className={`w-6 h-6 rounded-full mr-3 items-center justify-center ${option.isCorrect ? 'bg-success' : 'bg-border'
                              }`}>
                              <Text className={`text-12 font-semibold ${option.isCorrect ? 'text-white' : 'text-text-light'
                                }`}>
                                {['A', 'B', 'C', 'D'][optionIndex]}
                              </Text>
                            </View>
                            <Text className={`flex-1 text-14 ${option.isCorrect ? 'text-success font-semibold' : 'text-text'
                              }`}>
                              {option.text}
                            </Text>
                          </View>
                        ))}
                      </View>
                    )}
                  </TouchableOpacity>
                </View>
              );
            })}
          </View>
        </Card>
      </ScrollView>

      {/* Tips Modal */}
      <Modal
        visible={showTipsModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowTipsModal(false)}
      >
        <SafeAreaView className="flex-1 bg-background">
          {/* Modal Header */}
          <View className="flex-row items-center justify-between p-4 border-b border-border bg-white">
            <View className="flex-row items-center">
              <View className="w-10 h-10 bg-warning/10 rounded-full items-center justify-center mr-3">
                <Ionicons name="bulb" size={20} color={COLORS.WARNING} />
              </View>
              <View>
                <Text className="text-18 font-bold text-text">考車筆試懶人包</Text>
                <Text className="text-14 text-text-light">第{chineseVolumeNumber}冊重點提示</Text>
              </View>
            </View>
            <TouchableOpacity
              onPress={() => setShowTipsModal(false)}
              className="w-8 h-8 items-center justify-center rounded-full bg-gray-100"
            >
              <Ionicons name="close" size={20} color={COLORS.TEXT_LIGHT} />
            </TouchableOpacity>
          </View>

          {/* Modal Content */}
          <ScrollView className="flex-1 p-4" showsVerticalScrollIndicator={true}>
            <Card className="shadow-sm">
              <View className="p-4">
                <Text className="text-14 text-text leading-6 whitespace-pre-line">
                  {VOLUME_TIPS[volumeNumber as keyof typeof VOLUME_TIPS] || '此冊別暫無特別提示'}
                </Text>
              </View>
            </Card>
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}
