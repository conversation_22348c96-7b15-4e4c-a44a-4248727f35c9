import { router, useNavigation } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useLayoutEffect, useState } from 'react';
import { Platform, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { Button, ConfirmationModal } from '../../src/components/common';
import {
  OptionsList,
  QuestionCard
} from '../../src/components/question';
import { useRealtimeAnswer } from '../../src/hooks/useRealtimeAnswer';
import { usePracticeStore } from '../../src/store/usePracticeStore';
import { COLORS } from '../../src/utils/constants';

export default function PracticeSessionScreen() {
  const navigation = useNavigation();
  const quickAnswerMode = true; // Always use quick answer mode

  const {
    session,
    currentQuestion,
    selectedAnswer,
    showAnswer,
    stats,
    isActive,
    selectAnswer,
    submitAnswer,
    nextQuestion,
    endSession,
  } = usePracticeStore();

  const { recordAnswer } = useRealtimeAnswer();
  const [showCompleteModal, setShowCompleteModal] = useState(false);
  const [showEndModal, setShowEndModal] = useState(false);
  const [elapsedTime, setElapsedTime] = useState(0); // Timer starting from 0

  // Timer functionality
  useEffect(() => {
    if (!session || !isActive) {
      return;
    }

    const timer = setInterval(() => {
      setElapsedTime(prev => prev + 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [session, isActive]);

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const renderTimer = () => (
    <View className="px-3 py-2 rounded-[20px] w-[100px] text-center">
      <View className="flex-row items-center justify-center gap-1.5">
        <MaterialIcons
          name="schedule"
          size={16}
          color={COLORS.SECONDARY_TEXT}
        />
        <Text className="text-base font-semibold text-text">
          {formatTime(elapsedTime)}
        </Text>
      </View>
    </View>
  );

  // Dynamic header configuration
  useLayoutEffect(() => {
    navigation.setOptions({
      headerRight: () => renderTimer(),
      headerLeft: () => (
        <TouchableOpacity onPress={() => handleEndSession()} className="pr-2">
          <Ionicons name="chevron-back" size={28} color="black" />
        </TouchableOpacity>
      ),
    });
  }, [navigation, elapsedTime]);

  useEffect(() => {
    if (!session || !isActive) {
      router.dismissTo('/');
    }
  }, [session, isActive]);

  if (!session || !currentQuestion) {
    return null;
  }

  const handleAnswerSelect = (answerIndex: number) => {
    if (showAnswer) return;
    selectAnswer(answerIndex);

    if (quickAnswerMode) {
      handleSubmitAnswer(answerIndex);
    }
  };

  const handleSubmitAnswer = async (answer?: number) => {
    const finalAnswer = answer ?? selectedAnswer;
    if (finalAnswer === null || showAnswer) return;

    // Get current practice store state to access answerStartTime
    const state = usePracticeStore.getState();
    const answerTime = state.answerStartTime;
    const timeSpent = answerTime ? Math.round((Date.now() - answerTime.getTime()) / 1000) : 0;

    submitAnswer();

    // Record answer in database
    try {
      const correctOptionIndex = currentQuestion.options.findIndex(opt => opt.isCorrect);
      const selectedOptionLabel = ['A', 'B', 'C', 'D'][finalAnswer];
      const correctOptionLabel = ['A', 'B', 'C', 'D'][correctOptionIndex];

      await recordAnswer({
        questionId: currentQuestion.id,
        volume: currentQuestion.volume,
        chapter: 1, // Default chapter
        isCorrect: currentQuestion.options[finalAnswer]?.isCorrect || false,
        mode: 'practice',
        sessionId: session.id,
        selectedOption: selectedOptionLabel as 'A' | 'B' | 'C' | 'D',
        correctOption: correctOptionLabel as 'A' | 'B' | 'C' | 'D',
        timeSpent: Math.max(1, timeSpent), // Minimum 1 second
      });
    } catch (error) {
      console.error('Failed to record answer:', error);
      // Don't block the UI if recording fails
    }
  };

  const handleNext = () => {
    if (session.currentIndex < session.questions.length - 1) {
      nextQuestion();
    } else {
      // Reached end of current question set - offer to continue or finish
      setShowCompleteModal(true);
    }
  };

  const handleConfirmComplete = () => {
    setShowCompleteModal(false);
    endSession();
    router.dismissTo('/');
  };

  const handleCancelComplete = () => {
    setShowCompleteModal(false);
  };



  const handleEndSession = () => {
    setShowEndModal(true);
  };

  const handleConfirmEnd = () => {
    setShowEndModal(false);
    endSession();
    router.dismissTo('/');
  };

  const handleCancelEnd = () => {
    setShowEndModal(false);
  };

  const isLastQuestion = session.currentIndex === session.questions.length - 1;

  return (
    <SafeAreaView className="flex-1 bg-background" edges={['bottom', 'left', 'right']}>
      <StatusBar style="auto" />

      <ScrollView className="flex-1 px-4 py-4" showsVerticalScrollIndicator={false}>
        <QuestionCard
          question={currentQuestion}
          questionNumber={session.currentIndex + 1}
          totalQuestions={session.questions.length}
        />

        <OptionsList
          options={currentQuestion.options}
          selectedOption={selectedAnswer !== null ? selectedAnswer : undefined}
          onOptionSelect={handleAnswerSelect}
          showAnswer={showAnswer}
          disabled={showAnswer}
        />
      </ScrollView>

      {/* Sticky bottom single action */}
      <View className={`bg-background border-t border-border p-4 ${Platform.OS === 'ios' ? '' : 'mb-5'}`}>
        {showAnswer ? (
          <View className="flex-row justify-center gap-3">
            <Button
              title={isLastQuestion ? "繼續練習" : "下一題"}
              onPress={handleNext}
              style={{ flex: 1, backgroundColor: COLORS.THEME }}
              size='medium'
            />
          </View>
        ) : null}
      </View>

      {/* Completion Modal */}
      <ConfirmationModal
        visible={showCompleteModal}
        title="已完成當前題目"
        content={[
          `您已完成當前所有題目！`,
          `正確率：${stats.accuracy.toFixed(1)}%\n答對：${stats.correctCount} 題\n答錯：${stats.wrongCount} 題\n\n建議繼續練習其他題目來鞏固知識。`
        ]}
        onCancel={handleCancelComplete}
        onConfirm={handleConfirmComplete}
        cancelText="結束練習"
        confirmText="繼續練習"
      />

      {/* End Session Modal */}
      <ConfirmationModal
        visible={showEndModal}
        title="結束練習"
        content={[
          `本次練習統計：`,
          `已完成：${stats.answeredCount} 題\n答對：${stats.correctCount} 題\n答錯：${stats.wrongCount} 題\n正確率：${stats.accuracy.toFixed(1)}%\n\n確定要結束練習嗎？`
        ]}
        onCancel={handleCancelEnd}
        onConfirm={handleConfirmEnd}
        cancelText="取消"
        confirmText="結束練習"
      />
    </SafeAreaView>
  );
}