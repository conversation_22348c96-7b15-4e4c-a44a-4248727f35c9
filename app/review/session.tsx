import { router, useNavigation } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useLayoutEffect, useState } from 'react';
import { Platform, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { Button, ConfirmationModal } from '../../src/components/common';
import {
  OptionsList,
  QuestionCard
} from '../../src/components/question';
import { useRealtimeAnswer } from '../../src/hooks/useRealtimeAnswer';
import { WrongQuestionsService } from '../../src/services/questions/wrongQuestionsService';
import { useWrongQuestionsStore } from '../../src/store/useWrongQuestionsStore';
import { COLORS } from '../../src/utils/constants';

export default function WrongQuestionsSessionScreen() {
  const navigation = useNavigation();
  const quickAnswerMode = true; // Always use quick answer mode

  const {
    isActive,
    sessionId,
    questions,
    currentIndex,
    currentQuestion,
    selectedAnswer,
    showAnswer,
    isAnswerCorrect,
    stats,
    fixedTotalQuestions,
    questionNumber,
    selectAnswer,
    submitAnswer,
    nextQuestion,
    removeCurrentQuestion,
    endSession,
  } = useWrongQuestionsStore();

  const { recordAnswer } = useRealtimeAnswer();
  const [showCompleteModal, setShowCompleteModal] = useState(false);
  const [showEndModal, setShowEndModal] = useState(false);
  const [elapsedTime, setElapsedTime] = useState(0); // Timer starting from 0

  // Timer functionality
  useEffect(() => {
    if (!isActive) {
      return;
    }

    const timer = setInterval(() => {
      setElapsedTime(prev => prev + 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [isActive]);

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const renderTimer = () => (
    <View className="px-3 py-2 rounded-2xl w-25">
      <View className="flex-row items-center justify-center gap-1.5">
        <MaterialIcons
          name="schedule"
          size={16}
          color={COLORS.SECONDARY_TEXT}
        />
        <Text className="text-base font-semibold text-text">
          {formatTime(elapsedTime)}
        </Text>
      </View>
    </View>
  );

  // Dynamic header configuration
  useLayoutEffect(() => {
    navigation.setOptions({
      headerRight: () => renderTimer(),
      headerLeft: () => (
        <TouchableOpacity onPress={() => handleEndSession()} className="pr-2">
          <Ionicons name="chevron-back" size={28} color="black" />
        </TouchableOpacity>
      ),
    });
  }, [navigation, elapsedTime]);

  useEffect(() => {
    if (!isActive || !sessionId) {
      router.dismissTo('/');
    }
  }, [isActive, sessionId]);

  if (!isActive || !currentQuestion) {
    return null;
  }

  const handleAnswerSelect = (answerIndex: number) => {
    if (showAnswer) return;
    selectAnswer(answerIndex);

    if (quickAnswerMode) {
      handleSubmitAnswer(answerIndex);
    }
  };

  const handleSubmitAnswer = async (answer?: number) => {
    const finalAnswer = answer ?? selectedAnswer;
    if (finalAnswer === null || showAnswer) return;

    submitAnswer();

    // Record answer in database
    try {
      const correctOptionIndex = currentQuestion.options.findIndex(opt => opt.isCorrect);
      const selectedOptionLabel = ['A', 'B', 'C', 'D'][finalAnswer];
      const correctOptionLabel = ['A', 'B', 'C', 'D'][correctOptionIndex];
      
      await recordAnswer({
        questionId: currentQuestion.id,
        volume: currentQuestion.volume,
        chapter: 1, // Default chapter
        isCorrect: currentQuestion.options[finalAnswer]?.isCorrect || false,
        mode: 'practice', // Use practice mode for wrong questions review
        sessionId: sessionId!,
        selectedOption: selectedOptionLabel as 'A' | 'B' | 'C' | 'D',
        correctOption: correctOptionLabel as 'A' | 'B' | 'C' | 'D',
        timeSpent: 30, // Default time
      });

      // If answer is correct, mark as mastered in review mode (preserves wrong_attempts history)
      if (currentQuestion.options[finalAnswer]?.isCorrect) {
        await WrongQuestionsService.markQuestionAsMastered(currentQuestion.id, currentQuestion.volume);
      }
    } catch (error) {
      console.error('Failed to record answer:', error);
      // Don't show error to user, just log it
    }
  };

  const handleNext = () => {
    if (isAnswerCorrect && currentQuestion && !isLastQuestion) {
      // Remove from current session if answered correctly
      removeCurrentQuestion();
    } else {
      nextQuestion();
    }

    // Check if all questions completed
    if (isLastQuestion) {
      // If this is the last question, complete the review
      handleSessionComplete();
    }
  };

  const handleSessionComplete = () => {
    setShowCompleteModal(true);
  };

  const handleConfirmComplete = () => {
    setShowCompleteModal(false);
    endSession();
    router.dismissTo('/');
    if (isAnswerCorrect && currentQuestion) {
      removeCurrentQuestion();
    }
  };

  const handleEndSession = () => {
    setShowEndModal(true);
  };

  const handleConfirmEnd = () => {
    setShowEndModal(false);
    endSession();
    router.dismissTo('/');
  };

  const handleCancelEnd = () => {
    setShowEndModal(false);
  };

  const isLastQuestion = currentIndex >= questions.length - 1;

  return (
    <SafeAreaView className="flex-1 bg-background" edges={['bottom', 'left', 'right']}>
      <StatusBar style="auto" />

      <ScrollView className="flex-1 pt-2.5 p-5" showsVerticalScrollIndicator={false}>
        {/* Question Card */}
        <QuestionCard
          question={currentQuestion}
          questionNumber={questionNumber}
          totalQuestions={fixedTotalQuestions}
        />

        {/* Options List */}
        <OptionsList
          options={currentQuestion.options}
          selectedOption={selectedAnswer !== null ? selectedAnswer : undefined}
          onOptionSelect={handleAnswerSelect}
          showAnswer={showAnswer}
          disabled={showAnswer}
        />
      </ScrollView>

      {/* Sticky bottom single action (matching practice) */}
      <View className={`bg-background border-t border-border p-4 ${Platform.OS === 'ios' ? '' : 'mb-5'}`}>
        {showAnswer ? (
          <View className="flex-row justify-between gap-3">
            <Button
              title={isLastQuestion ? "完成複習" : "下一題"}
              onPress={handleNext}
              style={{ flex: 1, backgroundColor: COLORS.THEME }}
              size='medium'
            />
          </View>
        ) : null}
      </View>

      {/* Completion Modal */}
      <ConfirmationModal
        visible={showCompleteModal}
        title="複習完成"
        content={[
          `您已完成錯題複習！`,
          `答對：${stats.correctCount}題\n答錯：${stats.wrongCount}題\n準確率：${Math.round(stats.accuracy)}%`
        ]}
        onCancel={() => setShowCompleteModal(false)}
        onConfirm={handleConfirmComplete}
        cancelText="繼續複習"
        confirmText="返回主頁"
      />

      {/* End Session Modal */}
      <ConfirmationModal
        visible={showEndModal}
        title="結束複習"
        content={[
          `本次複習統計：`,
          `已完成：${stats.answeredCount} 題\n答對：${stats.correctCount} 題\n答錯：${stats.wrongCount} 題\n準確率：${Math.round(stats.accuracy)}%\n\n確定要結束複習嗎？`
        ]}
        onCancel={handleCancelEnd}
        onConfirm={handleConfirmEnd}
        cancelText="取消"
        confirmText="結束複習"
      />
    </SafeAreaView>
  );
}

