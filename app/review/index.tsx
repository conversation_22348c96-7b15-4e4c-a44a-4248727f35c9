import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useState } from 'react';
import { Al<PERSON>, ScrollView, Text, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { <PERSON><PERSON>, LoadingSpinner } from '../../src/components/common';
import { WrongQuestionsService } from '../../src/services/questions/wrongQuestionsService';
import { useWrongQuestionsStore } from '../../src/store/useWrongQuestionsStore';
import { ProcessedQuestion } from '../../src/types/question';
import { COLORS } from '../../src/utils/constants';

export default function WrongQuestionsReviewScreen() {
  const [loading, setLoading] = useState(true);
  const [wrongQuestionsCount, setWrongQuestionsCount] = useState(0);
  const [wrongQuestions, setWrongQuestions] = useState<ProcessedQuestion[]>([]);

  const { startReviewSession } = useWrongQuestionsStore();

  useEffect(() => {
    loadWrongQuestions();
  }, []);

  const loadWrongQuestions = async () => {
    try {
      setLoading(true);

      const [questions, count] = await Promise.all([
        WrongQuestionsService.getWrongQuestions(),
        WrongQuestionsService.getWrongQuestionsCount()
      ]);

      setWrongQuestions(questions);
      setWrongQuestionsCount(count);
    } catch (error) {
      console.error('Failed to load wrong questions:', error);
      Alert.alert('載入失敗', '無法載入錯題集，請稍後再試。');
    } finally {
      setLoading(false);
    }
  };

  const handleStartReview = () => {
    if (wrongQuestions.length === 0) {
      Alert.alert('暫無錯題', '您目前沒有錯題需要複習！');
      return;
    }

    startReviewSession(wrongQuestions);
    router.replace('/review/session' as any);
  };

  const getVolumeDistribution = () => {
    const volumeCount = new Map<number, number>();

    wrongQuestions.forEach(question => {
      const count = volumeCount.get(question.volume) || 0;
      volumeCount.set(question.volume, count + 1);
    });

    return Array.from(volumeCount.entries()).sort((a, b) => a[0] - b[0]);
  };

  if (loading) {
    return (
      <SafeAreaView className="flex-1 bg-background">
        <LoadingSpinner message="正在載入錯題集..." />
      </SafeAreaView>
    );
  }

  const volumeDistribution = getVolumeDistribution();

  return (
    <View className="flex-1 bg-background">
      <StatusBar style="auto" backgroundColor={COLORS.BACKGROUND} />

      <ScrollView
        contentContainerStyle={{ paddingVertical: 24, paddingHorizontal: 20 }}
        showsVerticalScrollIndicator={false}
      >
        {/* Statistics Card */}
        <View className="bg-card rounded-2xl p-5 mb-5 shadow-sm">
          <View className="flex-row justify-between items-center mb-4">
            <Text className="text-18 font-semibold text-text">錯題統計</Text>
            <View className="bg-accent px-3 py-1.5 rounded-2xl">
              <Text className="text-card text-base font-bold">{wrongQuestionsCount}</Text>
            </View>
          </View>

          {wrongQuestionsCount > 0 ? (
            <View className="gap-3">
              {volumeDistribution.map(([volume, count]) => (
                <View key={volume} className="flex-row justify-between items-center py-2">
                  <Text className="text-base text-text font-medium">第{volume}冊</Text>
                  <Text className="text-base text-text-secondary font-semibold">{count}題</Text>
                </View>
              ))}
            </View>
          ) : (
            <View className="items-center py-5">
              <Text className="text-40 mb-3">🎉</Text>
              <Text className="text-20 font-semibold text-text mb-2">太棒了！</Text>
              <Text className="text-base text-text-secondary text-center">您目前沒有錯題需要複習</Text>
            </View>
          )}
        </View>

        {/* Description */}
        {wrongQuestionsCount > 0 && (
          <View className="bg-card rounded-2xl p-5 mb-6 shadow-sm">
            <Text className="text-18 font-bold text-text mb-2 text-center">複習說明</Text>
            <Text className="text-15 text-text leading-[22px] mb-1">
              • 複習您曾經答錯的題目，加強薄弱環節
            </Text>
            <Text className="text-15 text-text leading-[22px] mb-1">
              • 複習過程中答對的題目將從錯題集中移除
            </Text>
            <Text className="text-15 text-text leading-[22px] mb-1">
              • 答錯的題目將保留在錯題集中
            </Text>
          </View>
        )}

        {/* Action Buttons */}
        <View className="gap-3">
          <Button
            size="large"
            title={wrongQuestionsCount > 0 ? "開始複習錯題" : "返回主頁"}
            onPress={wrongQuestionsCount > 0 ? handleStartReview : () => router.back()}
          />
        </View>
      </ScrollView>
    </View>
  );
}

